2025-08-19 15:49:07,492 - INFO - ✅ LLM (glm-4.5) 初始化成功。
2025-08-19 15:49:07,494 - INFO - 🧹 检测到旧的历史记录目录，正在清理 './chroma_db'...
2025-08-19 15:49:07,495 - INFO - ✅ 旧的历史记录已成功清理。
2025-08-19 15:49:08,019 - INFO - ✅ 向量化历史管理器初始化成功，数据将持久化到 './chroma_db' (遥测已禁用)。
2025-08-19 15:49:08,185 - INFO - ✅ 成功加载 3 个MCP工具
2025-08-19 15:49:08,195 - INFO - 
--- 自主智能体已准备就绪 ---
2025-08-19 15:49:13,738 - INFO - HTTP Request: POST http://**************:8301/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-19 15:49:15,550 - INFO - 📞 工具调用: list_directory 参数: {
  "directory_path": "C:\\Users\\<USER>\\Desktop\\dify-main",
  "tree_view": true
}
2025-08-19 15:49:15,550 - INFO - TOKEN_USAGE - Model: glm-4.5, Prompt: 1444, Completion: 135, Total: 1579
2025-08-19 15:49:15,652 - INFO - ✨工具输出: list_directory:
结果:
--- Tree view of C:\Users\<USER>\Desktop\dify-main ---
dify-main/
    .editorconfig
    .env.example
    .gitattributes
    .gitignore
    AUTHORS
    CONTRIBUTING.md
    CONTRIBUTING_CN.md
    CONTRIBUTING_DE.md
    CONTRIBUTING_ES.md
    CONTRIBUTING_FR.md
    CONTRIBUTING_JA.md
    CONTRIBUTING_KR.md
    CONTRIBUTING_PT.md
    CONTRIBUTING_TR.md
    CONTRIBUTING_TW.md
    CONTRIBUTING_VI.md
    LICENSE
    Makefile
    package-lock.json
    package.json
    README.md
    README_AR.md
    README_BN.md
    README_CN.md
    README_DE.md
    README_ES.md
    README_FR.md
    README_JA.md
    README_KL.md
    README_KR.md
    README_PT.md
    README_SI.md
    README_TR.md
    README_TW.md
    README_VI.md
    .devcontainer/
        devcontainer.json
        Dockerfile
        noop.txt
        post_create_command.sh
        post_start_command.sh
        README.md
        troubleshooting.png
    .github/
        CODE_OF_CONDUCT.md
        pull_request_template.md
        actions/
            setup-uv/
                action.yml
        DISCUSSION_TEMPLATE/
            general.yml
            help.yml
            suggestion.yml
        ISSUE_TEMPLATE/
            bug_report.yml
            chore.yaml
            config.yml
            feature_request.yml
            tracker.yml
        linters/
            .hadolint.yaml
            .isort.cfg
            .yaml-lint.yml
            editorconfig-checker.json
        workflows/
            api-tests.yml
            autofix.yml
            build-push.yml
            db-migration-test.yml
            deploy-dev.yml
            deploy-enterprise.yml
            deploy-rag-dev.yml
            docker-build.yml
            expose_service_ports.sh
            stale.yml
            style.yml
            tool-test-sdks.yaml
            translate-i18n-base-on-english.yml
            vdb-tests.yml
            web-tests.yml
    .vscode/
        launch.json.template
        README.md
    api/
        .dockerignore
        .env.example
        .ruff.toml
        app.py
        app_factory.py
        commands.py
        dify_app.py
        Dockerfile
        mypy.ini
        pyproject.toml
        pytest.ini
        README.md
        uv.lock
        .idea/
            icon.png
            vcs.xml
        .vscode/
            launch.json.example
        configs/
            app_config.py
            __init__.py
            deploy/
                __init__.py
            enterprise/
                __init__.py
            extra/
                notion_config.py
                sentry_config.py
                __init__.py
            feature/
                __init__.py
                hosted_service/
                    __init__.py
            middleware/
                __init__.py
                cache/
                    redis_config.py
                    __init__.py
                storage/
                    aliyun_oss_storage_config.py
                    amazon_s3_storage_config.py
                    azure_blob_storage_config.py
                    baidu_obs_storage_config.py
                    clickzetta_volume_storage_config.py
                    google_cloud_storage_config.py
                    huawei_obs_storage_config.py
                    oci_storage_config.py
                    opendal_storage_config.py
                    supabase_storage_config.py
                    tencent_cos_storage_config.py
                    volcengine_tos_storage_config.py
                vdb/
                    analyticdb_config.py
                    baidu_vector_config.py
                    chroma_config.py
                    clickzetta_config.py
                    couchbase_config.py
                    elasticsearch_config.py
                    huawei_cloud_config.py
                    lindorm_config.py
                    matrixone_config.py
                    milvus_config.py
                    myscale_config.py
                    oceanbase_config.py
                    opengauss_config.py
                    opensearch_config.py
                    oracle_config.py
                    pgvectors_config.py
                    pgvector_config.py
                    qdrant_config.py
                    relyt_config.py
                    tablestore_config.py
                    tencent_vector_config.py
                    tidb_on_qdrant_config.py
                    tidb_vector_config.py
                    upstash_config.py
                    vastbase_vector_config.py
                    vikingdb_config.py
                    weaviate_config.py
            observability/
                __init__.py
                otel/
                    otel_config.py
            packaging/
                pyproject.py
                __init__.py
            remote_settings_sources/
                base.py
                enums.py
                __init__.py
                apollo/
                    client.py
                    python_3x.py
                    utils.py
                    __init__.py
                nacos/
                    http_request.py
                    utils.py
                    __init__.py
        constants/
            languages.py
            mimetypes.py
            model_template.py
            recommended_apps.json
            tts_auto_play_timeout.py
            __init__.py
        contexts/
            wrapper.py
            __init__.py
        controllers/
            __init__.py
            common/
                errors.py
                fields.py
                helpers.py
            console/
                admin.py
                apikey.py
                error.py
                extension.py
                feature.py
                files.py
                init_validate.py
                ping.py
                remote_files.py
                setup.py
                version.py
                wraps.py
                __init__.py
                app/
                    advanced_prompt_template.py
                    agent.py
                    annotation.py
                    app.py
                    app_import.py
                    audio.py
                    completion.py
                    conversation.py
                    conversation_variables.py
                    error.py
                    generator.py
                    mcp_server.py
                    message.py
                    model_config.py
                    ops_trace.py
                    site.py
                    statistic.py
                    workflow.py
                    workflow_app_log.py
                    workflow_draft_variable.py
                    workflow_run.py
                    workflow_statistic.py
                    wraps.py
                    __init__.py
                auth/
                    activate.py
                    data_source_bearer_auth.py
                    data_source_oauth.py
                    error.py
                    forgot_password.py
                    login.py
                    oauth.py
                billing/
                    billing.py
                    compliance.py
                    __init__.py
                datasets/
                    datasets.py
                    datasets_document.py
                    datasets_segments.py
                    data_source.py
                    error.py
                    external.py
                    hit_testing.py
                    hit_testing_base.py
                    metadata.py
                    upload_file.py
                    website.py
                explore/
                    audio.py
                    completion.py
                    conversation.py
                    error.py
                    installed_app.py
                    message.py
                    parameter.py
                    recommended_app.py
                    saved_message.py
                    workflow.py
                    wraps.py
                tag/
                    tags.py
                workspace/
                    account.py
                    agent_providers.py
                    endpoint.py
                    error.py
                    load_balancing_config.py
                    members.py
                    models.py
                    model_providers.py
                    plugin.py
                    tool_providers.py
                    workspace.py
                    __init__.py
            files/
                error.py
                image_preview.py
                tool_files.py
                upload.py
                __init__.py
            inner_api/
                mail.py
                wraps.py
                __init__.py
                plugin/
                    plugin.py
                    wraps.py
                    __init__.py
                workspace/
                    workspace.py
                    __init__.py
            mcp/
                mcp.py
                __init__.py
            service_api/
                index.py
                wraps.py
                __init__.py
                app/
                    annotation.py
                    app.py
                    audio.py
                    completion.py
                    conversation.py
                    error.py
                    file.py
                    file_preview.py
                    message.py
                    site.py
                    workflow.py
                    __init__.py
                dataset/
                    dataset.py
                    document.py
                    error.py
                    hit_testing.py
                    metadata.py
                    segment.py
                    upload_file.py
                    __init__.py
                workspace/
                    models.py
            web/
                app.py
                audio.py
                completion.py
                conversation.py
                error.py
                feature.py
                files.py
                forgot_password.py
                login.py
                message.py
                passport.py
                remote_files.py
                saved_message.py
                site.py
                workflow.py
                wraps.py
                __init__.py
        core/
            hosting_configuration.py
            indexing_runner.py
            model_manager.py
            provider_manager.py
            __init__.py
            agent/
                base_agent_runner.py
                cot_agent_runner.py
                cot_chat_agent_runner.py
                cot_completion_agent_runner.py
                entities.py
                fc_agent_runner.py
                plugin_entities.py
                __init__.py
                output_parser/
                    cot_output_parser.py
                prompt/
                    template.py
                strategy/
                    base.py
                    plugin.py
            app/
                __init__.py
                apps/
                    base_app_generate_response_converter.py
                    base_app_generator.py
                    base_app_queue_manager.py
                    base_app_runner.py
                    exc.py
                    message_based_app_generator.py
                    message_based_app_queue_manager.py
                    workflow_app_runner.py
                    __init__.py
                    advanced_chat/
                        app_config_manager.py
                        app_generator.py
                        app_runner.py
                        generate_response_converter.py
                        generate_task_pipeline.py
                        __init__.py
                    agent_chat/
                        app_config_manager.py
                        app_generator.py
                        app_runner.py
                        generate_response_converter.py
                        __init__.py
                    chat/
                        app_config_manager.py
                        app_generator.py
                        app_runner.py
                        generate_response_converter.py
                        __init__.py
                    common/
                        workflow_response_converter.py
                        __init__.py
                    completion/
                        app_config_manager.py
                        app_generator.py
                        app_runner.py
                        generate_response_converter.py
                        __init__.py
                    workflow/
                        app_config_manager.py
                        app_generator.py
                        app_queue_manager.py
                        app_runner.py
                        generate_response_converter.py
                        generate_task_pipeline.py
                        __init__.py
                app_config/
                    base_app_config_manager.py
                    entities.py
                    __init__.py
                    common/
                        __init__.py
                        parameters_mapping/
                            __init__.py
                        sensitive_word_avoidance/
                            manager.py
                            __init__.py
                    easy_ui_based_app/
                        __init__.py
                        agent/
                            manager.py
                            __init__.py
                        dataset/
                            manager.py
                            __init__.py
                        model_config/
                            converter.py
                            manager.py
                            __init__.py
                        prompt_template/
                            manager.py
                            __init__.py
                        variables/
                            manager.py
                            __init__.py
                    features/
                        __init__.py
                        file_upload/
                            manager.py
                            __init__.py
                        more_like_this/
                            manager.py
                            __init__.py
                        opening_statement/
                            manager.py
                            __init__.py
                        retrieval_resource/
                            manager.py
                            __init__.py
                        speech_to_text/
                            manager.py
                            __init__.py
                        suggested_questions_after_answer/
                            manager.py
                            __init__.py
                        text_to_speech/
                            manager.py
                            __init__.py
                    workflow_ui_based_app/
                        __init__.py
                        variables/
                            manager.py
                            __init__.py
                entities/
                    app_invoke_entities.py
                    queue_entities.py
                    task_entities.py
                    __init__.py
                features/
                    __init__.py
                    annotation_reply/
                        annotation_reply.py
                        __init__.py
                    hosting_moderation/
                        hosting_moderation.py
                        __init__.py
                    rate_limiting/
                        rate_limit.py
                        __init__.py
                task_pipeline/
                    based_generate_task_pipeline.py
                    easy_ui_based_generate_task_pipeline.py
                    exc.py
                    message_cycle_manager.py
                    __init__.py
            base/
                __init__.py
                tts/
                    app_generator_tts_publisher.py
                    __init__.py
            callback_handler/
                agent_tool_callback_handler.py
                index_tool_callback_handler.py
                workflow_tool_callback_handler.py
                __init__.py
            entities/
                agent_entities.py
                embedding_type.py
                knowledge_entities.py
                model_entities.py
                parameter_entities.py
                provider_configuration.py
                provider_entities.py
                __init__.py
            errors/
                error.py
                __init__.py
            extension/
                api_based_extension_requestor.py
                extensible.py
                extension.py
                __init__.py
            external_data_tool/
                base.py
                external_data_fetch.py
                factory.py
                __init__.py
                api/
                    api.py
                    __builtin__
                    __init__.py
            file/
                constants.py
                enums.py
                file_manager.py
                helpers.py
                models.py
                tool_file_parser.py
                __init__.py
            helper/
                download.py
                encrypter.py
                marketplace.py
                model_provider_cache.py
                moderation.py
                module_import_helper.py
                position_helper.py
                provider_cache.py
                ssrf_proxy.py
                tool_parameter_cache.py
                trace_id_helper.py
                __init__.py
                code_executor/
                    code_executor.py
                    code_node_provider.py
                    template_transformer.py
                    __init__.py
                    javascript/
                        javascript_code_provider.py
                        javascript_transformer.py
                        __init__.py
                    jinja2/
                        jinja2_formatter.py
                        jinja2_transformer.py
                        __init__.py
                    python3/
                        python3_code_provider.py
                        python3_transformer.py
                        __init__.py
            llm_generator/
                llm_generator.py
                prompts.py
                __init__.py
                output_parser/
                    errors.py
                    rule_config_generator.py
                    structured_output.py
                    suggested_questions_after_answer.py
                    __init__.py
            mcp/
                entities.py
                error.py
                mcp_client.py
                types.py
                utils.py
                __init__.py
                auth/
                    auth_flow.py
                    auth_provider.py
                client/
                    sse_client.py
                    streamable_client.py
                server/
                    streamable_http.py
                session/
                    base_session.py
                    client_session.py
            memory/
                token_buffer_memory.py
            model_runtime/
                README.md
                README_CN.md
                __init__.py
                callbacks/
                    base_callback.py
                    logging_callback.py
                    __init__.py
                docs/
                    en_US/
                        customizable_model_scale_out.md
                        interfaces.md
                        predefined_model_scale_out.md
                        provider_scale_out.md
                        schema.md
                        images/
                            index/
                                image-1.png
                                image-2.png
                                image-20231210143654461.png
                                image-20231210144229650.png
                                image-20231210144814617.png
                                image-20231210151548521.png
                                image-20231210151628992.png
                                image-20231210165243632.png
                                image-3.png
                                image.png
                    zh_Hans/
                        customizable_model_scale_out.md
                        interfaces.md
                        predefined_model_scale_out.md
                        provider_scale_out.md
                        schema.md
                        images/
                            index/
                                image-1.png
                                image-2.png
                                image-20231210143654461.png
                                image-20231210144229650.png
                                image-20231210144814617.png
                                image-20231210151548521.png
                                image-20231210151628992.png
                                image-20231210165243632.png
                                image-3.png
                                image.png
                entities/
                    common_entities.py
                    defaults.py
                    llm_entities.py
                    message_entities.py
                    model_entities.py
                    provider_entities.py
                    rerank_entities.py
                    text_embedding_entities.py
                    __init__.py
                errors/
                    invoke.py
                    validate.py
                    __init__.py
                model_providers/
                    model_provider_factory.py
                    _position.yaml
                    __init__.py
                    __base/
                        ai_model.py
                        large_language_model.py
                        moderation_model.py
                        rerank_model.py
                        speech2text_model.py
                        text_embedding_model.py
                        tts_model.py
                        __init__.py
                        tokenizers/
                            gpt2_tokenizer.py
                schema_validators/
                    common_validator.py
                    model_credential_schema_validator.py
                    provider_credential_schema_validator.py
                    __init__.py
                utils/
                    encoders.py
                    __init__.py
            moderation/
                base.py
                factory.py
                input_moderation.py
                output_moderation.py
                __init__.py
                api/
                    api.py
                    __builtin__
                    __init__.py
                keywords/
                    keywords.py
                    __builtin__
                    __init__.py
                openai_moderation/
                    openai_moderation.py
                    __builtin__
                    __init__.py
            ops/
                base_trace_instance.py
                ops_trace_manager.py
                utils.py
                __init__.py
                aliyun_trace/
                    aliyun_trace.py
                    __init__.py
                    data_exporter/
                        traceclient.py
                        __init__.py
                    entities/
                        aliyun_trace_entity.py
                        semconv.py
                        __init__.py
                arize_phoenix_trace/
                    arize_phoenix_trace.py
                    __init__.py
                entities/
                    config_entity.py
                    trace_entity.py
                    __init__.py
                langfuse_trace/
                    langfuse_trace.py
                    __init__.py
                    entities/
                        langfuse_trace_entity.py
                        __init__.py
                langsmith_trace/
                    langsmith_trace.py
                    __init__.py
                    entities/
                        langsmith_trace_entity.py
                        __init__.py
                opik_trace/
                    opik_trace.py
                    __init__.py
                weave_trace/
                    weave_trace.py
                    __init__.py
                    entities/
                        weave_trace_entity.py
                        __init__.py
            plugin/
                backwards_invocation/
                    app.py
                    base.py
                    encrypt.py
                    model.py
                    node.py
                    tool.py
                endpoint/
                    exc.py
                entities/
                    base.py
                    bundle.py
                    endpoint.py
                    marketplace.py
                    parameters.py
                    plugin.py
                    plugin_daemon.py
                    request.py
                impl/
                    agent.py
                    asset.py
                    base.py
                    debugging.py
                    dynamic_select.py
                    endpoint.py
                    exc.py
                    model.py
                    oauth.py
                    plugin.py
                    tool.py
                utils/
                    converter.py
            prompt/
                advanced_prompt_transform.py
                agent_history_prompt_transform.py
                prompt_transform.py
                simple_prompt_transform.py
                __init__.py
                entities/
                    advanced_prompt_entities.py
                    __init__.py
                prompt_templates/
                    advanced_prompt_templates.py
                    baichuan_chat.json
                    baichuan_completion.json
                    common_chat.json
                    common_completion.json
                    __init__.py
                utils/
                    extract_thread_messages.py
                    get_thread_messages_length.py
                    prompt_message_util.py
                    prompt_template_parser.py
                    __init__.py
            rag/
                __init__.py
                cleaner/
                    cleaner_base.py
                    clean_processor.py
                datasource/
                    retrieval_service.py
                    __init__.py
                    keyword/
                        keyword_base.py
                        keyword_factory.py
                        keyword_type.py
                        __init__.py
                        jieba/
                            jieba.py
                            jieba_keyword_table_handler.py
                            stopwords.py
                            __init__.py
                    vdb/
                        field.py
                        vector_base.py
                        vector_factory.py
                        vector_type.py
                        __init__.py
                        analyticdb/
                            analyticdb_vector.py
                            analyticdb_vector_openapi.py
                            analyticdb_vector_sql.py
                            __init__.py
                        baidu/
                            baidu_vector.py
                            __init__.py
                        chroma/
                            chroma_vector.py
                            __init__.py
                        clickzetta/
                            clickzetta_vector.py
                            README.md
                            __init__.py
                        couchbase/
                            couchbase_vector.py
                            __init__.py
                        elasticsearch/
                            elasticsearch_ja_vector.py
                            elasticsearch_vector.py
                            __init__.py
                        huawei/
                            huawei_cloud_vector.py
                            __init__.py
                        lindorm/
                            lindorm_vector.py
                            __init__.py
                        matrixone/
                            matrixone_vector.py
                            __init__.py
                        milvus/
                            milvus_vector.py
                            __init__.py
                        myscale/
                            myscale_vector.py
                            __init__.py
                        oceanbase/
                            oceanbase_vector.py
                            __init__.py
                        opengauss/
                            opengauss.py
                            __init__.py
                        opensearch/
                            opensearch_vector.py
                            __init__.py
                        oracle/
                            oraclevector.py
                            __init__.py
                        pgvector/
                            pgvector.py
                            __init__.py
                        pgvecto_rs/
                            collection.py
                            pgvecto_rs.py
                            __init__.py
                        pyvastbase/
                            vastbase_vector.py
                            __init__.py
                        qdrant/
                            qdrant_vector.py
                            __init__.py
                        relyt/
                            relyt_vector.py
                            __init__.py
                        tablestore/
                            tablestore_vector.py
                            __init__.py
                        tencent/
                            tencent_vector.py
                            __init__.py
                        tidb_on_qdrant/
                            tidb_on_qdrant_vector.py
                            tidb_service.py
                            __init__.py
                        tidb_vector/
                            tidb_vector.py
                            __init__.py
                        upstash/
                            upstash_vector.py
                            __init__.py
                        vikingdb/
                            vikingdb_vector.py
                            __init__.py
                        weaviate/
                            weaviate_vector.py
                            __init__.py
                data_post_processor/
                    data_post_processor.py
                    reorder.py
                    __init__.py
                docstore/
                    dataset_docstore.py
                    __init__.py
                embedding/
                    cached_embedding.py
                    embedding_base.py
                    retrieval.py
                    __init__.py
                entities/
                    citation_metadata.py
                    context_entities.py
                    metadata_entities.py
                extractor/
                    csv_extractor.py
                    excel_extractor.py
                    extractor_base.py
                    extract_processor.py
                    helpers.py
                    html_extractor.py
                    jina_reader_extractor.py
                    markdown_extractor.py
                    notion_extractor.py
                    pdf_extractor.py
                    text_extractor.py
                    word_extractor.py
                    blob/
                        blob.py
                    entity/
                        datasource_type.py
                        extract_setting.py
                    firecrawl/
                        firecrawl_app.py
                        firecrawl_web_extractor.py
                    unstructured/
                        unstructured_doc_extractor.py
                        unstructured_eml_extractor.py
                        unstructured_epub_extractor.py
                        unstructured_markdown_extractor.py
                        unstructured_msg_extractor.py
                        unstructured_pptx_extractor.py
                        unstructured_ppt_extractor.py
                        unstructured_xml_extractor.py
                    watercrawl/
                        client.py
                        exceptions.py
                        extractor.py
                        provider.py
                index_processor/
                    index_processor_base.py
                    index_processor_factory.py
                    __init__.py
                    constant/
                        built_in_field.py
                        index_type.py
                        __init__.py
                    processor/
                        paragraph_index_processor.py
                        parent_child_index_processor.py
                        qa_index_processor.py
                        __init__.py
                models/
                    document.py
                    __init__.py
                rerank/
                    rerank_base.py
                    rerank_factory.py
                    rerank_model.py
                    rerank_type.py
                    weight_rerank.py
                    __init__.py
                    entity/
                        weight.py
                retrieval/
                    dataset_retrieval.py
                    retrieval_methods.py
                    template_prompts.py
                    __init__.py
                    output_parser/
                        react_output.py
                        structured_chat.py
                        __init__.py
                    router/
                        multi_dataset_function_call_router.py
                        multi_dataset_react_route.py
                splitter/
                    fixed_text_splitter.py
                    text_splitter.py
                    __init__.py
            repositories/
                factory.py
                sqlalchemy_workflow_execution_repository.py
                sqlalchemy_workflow_node_execution_repository.py
                __init__.py
            tools/
                errors.py
                signature.py
                tool_engine.py
                tool_file_manager.py
                tool_label_manager.py
                tool_manager.py
                __init__.py
                builtin_tool/
                    provider.py
                    tool.py
                    _position.yaml
                    providers/
                        _positions.py
                        __init__.py
                        audio/
                            audio.py
                            audio.yaml
                            tools/
                                asr.py
                                asr.yaml
                                tts.py
                                tts.yaml
                            _assets/
                                icon.svg
                        code/
                            code.py
                            code.yaml
                            tools/
                                simple_code.py
                                simple_code.yaml
                            _assets/
                                icon.svg
                        time/
                            time.py
                            time.yaml
                            tools/
                                current_time.py
                                current_time.yaml
                                localtime_to_timestamp.py
                                localtime_to_timestamp.yaml
                                timestamp_to_localtime.py
                                timestamp_to_localtime.yaml
                                timezone_conversion.py
                                timezone_conversion.yaml
                                weekday.py
                                weekday.yaml
                            _assets/
                                icon.svg
                        webscraper/
                            webscraper.py
                            webscraper.yaml
                            tools/
                                webscraper.py
                                webscraper.yaml
                            _assets/
                                icon.svg
                custom_tool/
                    provider.py
                    tool.py
                entities/
                    agent_entities.py
                    api_entities.py
                    common_entities.py
                    constants.py
                    file_entities.py
                    tool_bundle.py
                    tool_entities.py
                    values.py
                mcp_tool/
                    provider.py
                    tool.py
                plugin_tool/
                    provider.py
                    tool.py
                utils/
                    configuration.py
                    dataset_retriever_tool.py
                    encryption.py
                    message_transformer.py
                    model_invocation_utils.py
                    parser.py
                    rag_web_reader.py
                    system_oauth_encryption.py
                    text_processing_utils.py
                    uuid_utils.py
                    web_reader_tool.py
                    workflow_configuration_sync.py
                    yaml_utils.py
                    __init__.py
                    dataset_retriever/
                        dataset_multi_retriever_tool.py
                        dataset_retriever_base_tool.py
                        dataset_retriever_tool.py
                workflow_as_tool/
                    provider.py
                    tool.py
                __base/
                    tool.py
                    tool_provider.py
                    tool_runtime.py
            variables/
                consts.py
                exc.py
                segments.py
                segment_group.py
                types.py
                utils.py
                variables.py
                __init__.py
            workflow/
                constants.py
                conversation_variable_updater.py
                enums.py
                errors.py
                system_variable.py
                variable_loader.py
                workflow_cycle_manager.py
                workflow_engine_manager.py
                workflow_entry.py
                workflow_type_encoder.py
                __init__.py
                callbacks/
                    base_workflow_callback.py
                    workflow_logging_callback.py
                    __init__.py
                entities/
                    node_entities.py
                    variable_entities.py
                    variable_pool.py
                    workflow_execution.py
                    workflow_node_execution.py
                    __init__.py
                graph_engine/
                    graph_engine.py
                    __init__.py
                    condition_handlers/
                        base_handler.py
                        branch_identify_handler.py
                        condition_handler.py
                        condition_manager.py
                        __init__.py
                    entities/
                        event.py
                        graph.py
                        graph_init_params.py
                        graph_runtime_state.py
                        runtime_route_state.py
                        run_condition.py
                        __init__.py
                nodes/
                    enums.py
                    node_mapping.py
                    __init__.py
                    agent/
                        agent_node.py
                        entities.py
                        exc.py
                        __init__.py
                    answer/
                        answer_node.py
                        answer_stream_generate_router.py
                        answer_stream_processor.py
                        base_stream_processor.py
                        entities.py
                        __init__.py
                    base/
                        entities.py
                        exc.py
                        node.py
                        __init__.py
                    code/
                        code_node.py
                        entities.py
                        exc.py
                        __init__.py
                    document_extractor/
                        entities.py
                        exc.py
                        node.py
                        __init__.py
                    end/
                        end_node.py
                        end_stream_generate_router.py
                        end_stream_processor.py
                        entities.py
                        __init__.py
                    event/
                        event.py
                        types.py
                        __init__.py
                    http_request/
                        entities.py
                        exc.py
                        executor.py
                        node.py
                        __init__.py
                    if_else/
                        entities.py
                        if_else_node.py
                        __init__.py
                    iteration/
                        entities.py
                        exc.py
                        iteration_node.py
                        iteration_start_node.py
                        __init__.py
                    knowledge_retrieval/
                        entities.py
                        exc.py
                        knowledge_retrieval_node.py
                        template_prompts.py
                        __init__.py
                    list_operator/
                        entities.py
                        exc.py
                        node.py
                        __init__.py
                    llm/
                        entities.py
                        exc.py
                        file_saver.py
                        llm_utils.py
                        node.py
                        __init__.py
                    loop/
                        entities.py
                        loop_end_node.py
                        loop_node.py
                        loop_start_node.py
                        __init__.py
                    parameter_extractor/
                        entities.py
                        exc.py
                        parameter_extractor_node.py
                        prompts.py
                        __init__.py
                    question_classifier/
                        entities.py
                        exc.py
                        question_classifier_node.py
                        template_prompts.py
                        __init__.py
                    start/
                        entities.py
                        start_node.py
                        __init__.py
                    template_transform/
                        entities.py
                        template_transform_node.py
                        __init__.py
                    tool/
                        entities.py
                        exc.py
                        tool_node.py
                        __init__.py
                    variable_aggregator/
                        entities.py
                        variable_aggregator_node.py
                        __init__.py
                    variable_assigner/
                        __init__.py
                        common/
                            exc.py
                            helpers.py
                            impl.py
                            __init__.py
                        v1/
                            node.py
                            node_data.py
                            __init__.py
                        v2/
                            constants.py
                            entities.py
                            enums.py
                            exc.py
                            helpers.py
                            node.py
                            __init__.py
                repositories/
                    draft_variable_repository.py
                    workflow_execution_repository.py
                    workflow_node_execution_repository.py
                    __init__.py
                utils/
                    variable_template_parser.py
                    __init__.py
                    condition/
                        entities.py
                        processor.py
                        __init__.py
        docker/
            entrypoint.sh
        events/
            app_event.py
            dataset_event.py
            document_event.py
            message_event.py
            tenant_event.py
            __init__.py
            event_handlers/
                clean_when_dataset_deleted.py
                clean_when_document_deleted.py
                create_document_index.py
                create_installed_app_when_app_created.py
                create_site_record_when_app_created.py
                delete_tool_parameters_cache_when_sync_draft_workflow.py
                document_index_event.py
                update_app_dataset_join_when_app_model_config_updated.py
                update_app_dataset_join_when_app_published_workflow_updated.py
                update_provider_when_message_created.py
                __init__.py
        extensions/
            ext_app_metrics.py
            ext_blueprints.py
            ext_celery.py
            ext_code_based_extension.py
            ext_commands.py
            ext_compress.py
            ext_database.py
            ext_hosting_provider.py
            ext_import_modules.py
            ext_logging.py
            ext_login.py
            ext_mail.py
            ext_migrate.py
            ext_otel.py
            ext_proxy_fix.py
            ext_redis.py
            ext_request_logging.py
            ext_sentry.py
            ext_set_secretkey.py
            ext_storage.py
            ext_timezone.py
            ext_warnings.py
            __init__.py
            storage/
                aliyun_oss_storage.py
                aws_s3_storage.py
                azure_blob_storage.py
                baidu_obs_storage.py
                base_storage.py
                google_cloud_storage.py
                huawei_obs_storage.py
                opendal_storage.py
                oracle_oci_storage.py
                storage_type.py
                supabase_storage.py
                tencent_cos_storage.py
                volcengine_tos_storage.py
                clickzetta_volume/
                    clickzetta_volume_storage.py
                    file_lifecycle.py
                    volume_permissions.py
                    __init__.py
        factories/
            agent_factory.py
            file_factory.py
            variable_factory.py
            __init__.py
        fields/
            annotation_fields.py
            api_based_extension_fields.py
            app_fields.py
            conversation_fields.py
            conversation_variable_fields.py
            dataset_fields.py
            data_source_fields.py
            document_fields.py
            end_user_fields.py
            file_fields.py
            hit_testing_fields.py
            installed_app_fields.py
            member_fields.py
            message_fields.py
            raws.py
            segment_fields.py
            tag_fields.py
            workflow_app_log_fields.py
            workflow_fields.py
            workflow_run_fields.py
            _value_type_serializer.py
            __init__.py
        libs/
            datetime_utils.py
            email_i18n.py
            exception.py
            external_api.py
            file_utils.py
            flask_utils.py
            gmpy2_pkcs10aep_cipher.py
            helper.py
            infinite_scroll_pagination.py
            json_in_md_parser.py
            login.py
            oauth.py
            oauth_data_source.py
            passport.py
            password.py
            rsa.py
            sendgrid.py
            smtp.py
            uuid_utils.py
            __init__.py
        migrations/
            alembic.ini
            env.py
            README
            script.py.mako
            versions/
                00bacef91f18_rename_api_provider_description.py
                03f98355ba0e_add_workflow_tool_label_and_tool_.py
                04c602f5dc9b_update_appmodelconfig_and_add_table_.py
                053da0c1d756_add_api_tool_privacy.py
                114eed84c228_remove_tool_id_from_model_invoke.py
                161cadc1af8d_add_dataset_permission_tenant_id.py
                16830a790f0f_.py
                16fa53d9faec_add_provider_model_support.py
                17b5ab037c40_add_keyworg_table_storage_type.py
                187385f442fc_modify_provider_model_name_length.py
                2024_08_09_0801-1787fbae959a_update_tools_original_url_length.py
                2024_08_13_0633-63a83fcf12ba_support_conversation_variables.py
                2024_08_14_1354-8782057ff0dc_add_conversations_dialogue_count.py
                2024_08_15_0956-0251a1c768cc_add_tidb_auth_binding.py
                2024_08_15_1001-a6be81136580_app_and_site_icon_type.py
                2024_08_20_0455-2dbe42621d96_rename_workflow__conversation_variables_.py
                2024_08_25_0441-d0187d6a88dd_add_created_by_and_updated_by_to_app_.py
                2024_09_01_1255-030f4915f36a_add_use_icon_as_answer_icon_fields_for_.py
                2024_09_11_1012-d57ba9ebb251_add_parent_message_id_to_messages.py
                2024_09_24_0922-6af6a521a53e_update_retrieval_resource.py
                2024_09_25_0434-33f5fac87f29_external_knowledge_api.py
                2024_09_29_0835-ddcc8bbef391_increase_max_length_of_builtin_tool_provider.py
                2024_10_09_1329-d8e744d88ed6_fix_wrong_service_api_history.py
                2024_10_10_0516-bbadea11becb_add_name_and_size_to_tool_files.py
                2024_10_22_0959-43fa78bc3b7d_add_white_list.py
                2024_10_28_0720-08ec4f75af5e_add_tenant_plugin_permisisons.py
                2024_11_01_0434-d3f6769a94a3_add_upload_files_source_url.py
                2024_11_01_0449-93ad8c19c40b_rename_conversation_variables_index_name.py
                2024_11_01_0540-f4d7ce70a7ca_update_upload_files_source_url.py
                2024_11_01_0622-d07474999927_update_type_of_custom_disclaimer_to_text.py
                2024_11_01_0623-09a8d1878d9b_update_workflows_graph_features_and_.py
                2024_11_12_0925-01d6889832f7_add_created_at_index_for_messages.py
                2024_11_22_0701-e19037032219_parent_child_index.py
                2024_11_28_0553-cf8f4fc45278_add_exceptions_count_field_to_.py
                2024_12_19_1746-11b07f66c737_remove_unused_tool_providers.py
                2024_12_20_0628-e1944c35e15e_add_retry_index_field_to_node_execution_.py
                2024_12_23_1154-d7999dfa4aae_remove_workflow_node_executions_retry_.py
                2024_12_25_1137-923752d42eb6_add_auto_disabled_dataset_logs.py
                2025_01_01_2000-a91b476a53de_change_workflow_runs_total_tokens_to_.py
                2025_01_14_0617-f051706725cc_add_rate_limit_logs.py
                2025_02_27_0917-d20049ed0af6_add_metadata_function.py
                2025_03_03_0304-4413929e1ec2_extend_provider_name_column.py
                2025_03_03_1436-ee79d9b1c156_add_marked_name_and_marked_comment_in_.py
                2025_03_07_0315-5511c782ee4c_extend_provider_column.py
                2025_03_29_2227-6a9f914f656c_change_documentsegment_and_childchunk_.py
                2025_05_14_1403-d28f2004b072_add_index_for_workflow_conversation_.py
                2025_05_15_1531-2adcbe1f5dfb_add_workflowdraftvariable_model.py
                2025_06_06_1424-4474872b0ee6_workflow_draft_varaibles_add_node_execution_id.py
                2025_06_19_1633-0ab65e1cc7fa_remove_sequence_number_from_workflow_.py
                2025_06_25_0936-58eb7bdb93fe_add_mcp_server_tool_and_app_server.py
                2025_07_02_2332-1c9ba48be8e4_add_uuidv7_function_in_sql.py
                2025_07_04_1705-71f5020c6470_tool_oauth.py
                2025_07_21_0935-1a83934ad6d1_update_models.py
                2025_07_22_0019-375fe79ead14_oauth_refresh_token.py
                2025_07_23_1508-8bcc02c9bd07_add_tenant_plugin_autoupgrade_table.py
                2025_07_24_1450-532b3f888abf_manual_dataset_field_update.py
                23db93619b9d_add_message_files_into_agent_thought.py
                246ba09cbbdb_add_app_anntation_setting.py
                2a3aebbbf4bb_add_app_tracing.py
                2beac44e5f5f_add_is_universal_in_apps.py
                2c8af9671032_add_qa_document_language.py
                2e9819ca5b28_add_tenant_id_in_api_token.py
                380c6aa5a70d_add_tool_labels_to_agent_thought.py
                3b18fea55204_add_tool_label_bings.py
                3c7cac9521c6_add_tags_and_binding_table.py
                3ef9b2b6bee6_add_assistant_app.py
                408176b91ad3_add_max_active_requests.py
                42e85ed5564d_conversation_columns_set_nullable.py
                46976cc39132_add_annotation_histoiry_score.py
                47cc7df8c4f3_modify_default_model_name_length.py
                4823da1d26cf_add_tool_file.py
                4829e54d2fee_change_message_chain_id_to_nullable.py
                4bcffcd64aa4_update_dataset_model_field_null_.py
                4e99a8df00ff_add_load_balancing.py
                4ff534e1eb11_add_workflow_to_site.py
                5022897aaceb_add_model_name_in_embedding.py
                53bf8af60645_update_model.py
                563cf8bf777b_enable_tool_file_without_conversation_id.py
                5fda94355fce_custom_disclaimer.py
                614f77cecc48_add_last_active_at.py
                63f9175e515b_merge_branches.py
                64a70a7aab8b_add_workflow_run_index.py
                64b051264f32_init.py
                675b5321501b_add_node_execution_id_into_node_.py
                6dcb43972bdc_add_dataset_retriever_resource.py
                6e2cfb077b04_add_dataset_collection_binding.py
                6e957a32015b_add_embedding_cache_created_at_index.py
                714aafe25d39_add_anntation_history_match_response.py
                77e83833755c_add_app_config_retriever_resource.py
                7b45942e39bb_add_api_key_auth_binding.py
                7bdef072e63a_add_workflow_tool.py
                7ce5a52e4eee_add_tool_providers.py
                7e6a8693e07a_add_table_dataset_permissions.py
                853f9b9cd3b6_add_message_price_unit.py
                88072f0caa04_add_custom_config_in_tenant.py
                89c7899ca936_.py
                8ae9bc661daa_add_tool_conversation_variables_idx.py
                8d2d099ceb74_add_qa_model_support.py
                8e5588e6412e_add_environment_variable_to_workflow_.py
                8ec536f3c800_rename_api_provider_credentails.py
                8fe468ba0ca5_add_gpt4v_supports.py
                968fff4c0ab9_add_api_based_extension.py
                9e98fbaffb88_add_workflow_tool_version.py
                9f4e3427ea84_add_created_by_role.py
                9fafbd60eca1_add_message_file_belongs_to.py
                a45f4dfde53b_add_language_to_recommend_apps.py
                a5b56fb053ef_app_config_add_speech_to_text.py
                a8d7385a7b66_add_embeddings_provider_name.py
                a8f9b3c45e4a_add_tenant_id_db_index.py
                a9836e3baeee_add_external_data_tools_in_app_model_.py
                ab23c11305d4_add_dataset_query_variable_at_app_model_.py
                ad472b61a054_add_api_provider_icon.py
                b24be59fbb04_.py
                b2602e131636_add_workflow_run_id_index_for_message.py
                b289e2408ee2_add_workflow.py
                b3a09c049e8e_add_advanced_prompt_templates.py
                b5429b71023c_messages_columns_set_nullable.py
                b69ca54b9208_add_chatbot_color_theme.py
                bf0aec5ba2cf_add_provider_order.py
                c031d46af369_remove_app_model_config_trace_config_.py
                c3311b089690_add_tool_meta.py
                c71211c8f604_add_tool_invoke_model_log.py
                cc04d0998d4d_set_model_config_column_nullable.py
                d3d503a3471c_add_is_deleted_to_conversations.py
                de95f5c77138_migration_serpapi_api_key.py
                dfb3b7f477da_add_tool_index.py
                e1901f623fd0_add_annotation_reply.py
                e2eacc9a1b63_add_status_for_message.py
                e32f6ccb87c6_e08af0a69ccefbb59fa80c778efee300bb780980.py
                e35ed59becda_modify_quota_limit_field_type.py
                e8883b0148c9_add_dataset_model_name.py
                eeb2e349e6ac_increase_max_model_name_length.py
                f25003750af4_add_created_updated_at.py
                f2a6fc85e260_add_anntation_history_message_id.py
                f9107f83abab_add_desc_for_apps.py
                fca025d3b60f_add_dataset_retrival_model.py
                fecff1c3da27_remove_extra_tracing_app_config_table .py
        models/
            account.py
            api_based_extension.py
            base.py
            dataset.py
            engine.py
            enums.py
            model.py
            provider.py
            source.py
            task.py
            tools.py
            types.py
            web.py
            workflow.py
            _workflow_exc.py
            __init__.py
        repositories/
            api_workflow_node_execution_repository.py
            api_workflow_run_repository.py
            factory.py
            sqlalchemy_api_workflow_node_execution_repository.py
            sqlalchemy_api_workflow_run_repository.py
            __init__.py
        schedule/
            check_upgradable_plugin_task.py
            clean_embedding_cache_task.py
            clean_messages.py
            clean_unused_datasets_task.py
            create_tidb_serverless_task.py
            mail_clean_document_notify_task.py
            queue_monitor_task.py
            update_tidb_serverless_status_task.py
        services/
            account_service.py
            advanced_prompt_template_service.py
            agent_service.py
            annotation_service.py
            api_based_extension_service.py
            app_dsl_service.py
            app_generate_service.py
            app_model_config_service.py
            app_service.py
            audio_service.py
            billing_service.py
            clear_free_plan_tenant_expired_logs.py
            code_based_extension_service.py
            conversation_service.py
            dataset_service.py
            external_knowledge_service.py
            feature_service.py
            file_service.py
            hit_testing_service.py
            knowledge_service.py
            message_service.py
            metadata_service.py
            model_load_balancing_service.py
            model_provider_service.py
            operation_service.py
            ops_service.py
            recommended_app_service.py
            saved_message_service.py
            tag_service.py
            vector_service.py
            webapp_auth_service.py
            website_service.py
            web_conversation_service.py
            workflow_app_service.py
            workflow_draft_variable_service.py
            workflow_run_service.py
            workflow_service.py
            workspace_service.py
            __init__.py
            auth/
                api_key_auth_base.py
                api_key_auth_factory.py
                api_key_auth_service.py
                auth_type.py
                jina.py
                __init__.py
                firecrawl/
                    firecrawl.py
                    __init__.py
                jina/
                    jina.py
                    __init__.py
                watercrawl/
                    watercrawl.py
                    __init__.py
            enterprise/
                base.py
                enterprise_service.py
                mail_service.py
                __init__.py
            entities/
                model_provider_entities.py
                __init__.py
                external_knowledge_entities/
                    external_knowledge_entities.py
                knowledge_entities/
                    knowledge_entities.py
            errors/
                account.py
                app.py
                app_model_config.py
                audio.py
                base.py
                chunk.py
                conversation.py
                dataset.py
                document.py
                file.py
                index.py
                llm.py
                message.py
                plugin.py
                workflow_service.py
                workspace.py
                __init__.py
            plugin/
                data_migration.py
                dependencies_analysis.py
                endpoint_service.py
                github_service.py
                oauth_service.py
                plugin_auto_upgrade_service.py
                plugin_migration.py
                plugin_parameter_service.py
                plugin_permission_service.py
                plugin_service.py
                __init__.py
            recommend_app/
                recommend_app_base.py
                recommend_app_factory.py
                recommend_app_type.py
                __init__.py
                buildin/
                    buildin_retrieval.py
                    __init__.py
                database/
                    database_retrieval.py
                    __init__.py
                remote/
                    remote_retrieval.py
                    __init__.py
            tools/
                api_tools_manage_service.py
                builtin_tools_manage_service.py
                mcp_tools_manage_service.py
                tools_manage_service.py
                tools_transform_service.py
                tool_labels_service.py
                workflow_tools_manage_service.py
            workflow/
                workflow_converter.py
                __init__.py
        tasks/
            add_document_to_index_task.py
            batch_clean_document_task.py
            batch_create_segment_to_index_task.py
            clean_dataset_task.py
            clean_document_task.py
            clean_notion_document_task.py
            create_segment_to_index_task.py
            deal_dataset_vector_index_task.py
            delete_account_task.py
            delete_segment_from_index_task.py
            disable_segments_from_index_task.py
            disable_segment_from_index_task.py
            document_indexing_sync_task.py
            document_indexing_task.py
            document_indexing_update_task.py
            duplicate_document_indexing_task.py
            enable_segments_to_index_task.py
            enable_segment_to_index_task.py
            mail_account_deletion_task.py
            mail_change_mail_task.py
            mail_email_code_login.py
            mail_enterprise_task.py
            mail_invite_member_task.py
            mail_owner_transfer_task.py
            mail_reset_password_task.py
            ops_trace_task.py
            process_tenant_plugin_autoupgrade_check_task.py
            recover_document_indexing_task.py
            remove_app_and_related_data_task.py
            remove_document_from_index_task.py
            retry_document_indexing_task.py
            sync_website_document_indexing_task.py
            __init__.py
            annotation/
                add_annotation_to_index_task.py
                batch_import_annotations_task.py
                delete_annotation_index_task.py
                disable_annotation_reply_task.py
                enable_annotation_reply_task.py
                update_annotation_to_index_task.py
        templates/
            change_mail_completed_template_en-US.html
            change_mail_completed_template_zh-CN.html
            change_mail_confirm_new_template_en-US.html
            change_mail_confirm_new_template_zh-CN.html
            change_mail_confirm_old_template_en-US.html
            change_mail_confirm_old_template_zh-CN.html
            clean_document_job_mail_template-US.html
            delete_account_code_email_template_en-US.html
            delete_account_success_template_en-US.html
            email_code_login_mail_template_en-US.html
            email_code_login_mail_template_zh-CN.html
            invite_member_mail_template_en-US.html
            invite_member_mail_template_zh-CN.html
            queue_monitor_alert_email_template_en-US.html
            reset_password_mail_template_en-US.html
            reset_password_mail_template_zh-CN.html
            transfer_workspace_new_owner_notify_template_en-US.html
            transfer_workspace_new_owner_notify_template_zh-CN.html
            transfer_workspace_old_owner_notify_template_en-US.html
            transfer_workspace_old_owner_notify_template_zh-CN.html
            transfer_workspace_owner_confirm_template_en-US.html
            transfer_workspace_owner_confirm_template_zh-CN.html
            without-brand/
                change_mail_completed_template_en-US.html
                change_mail_completed_template_zh-CN.html
                change_mail_confirm_new_template_en-US.html
                change_mail_confirm_new_template_zh-CN.html
                change_mail_confirm_old_template_en-US.html
                change_mail_confirm_old_template_zh-CN.html
                email_code_login_mail_template_en-US.html
                email_code_login_mail_template_zh-CN.html
                invite_member_mail_template_en-US.html
                invite_member_mail_template_zh-CN.html
                reset_password_mail_template_en-US.html
                reset_password_mail_template_zh-CN.html
                transfer_workspace_new_owner_notify_template_en-US.html
                transfer_workspace_new_owner_notify_template_zh-CN.html
                transfer_workspace_old_owner_notify_template_en-US.html
                transfer_workspace_old_owner_notify_template_zh-CN.html
                transfer_workspace_owner_confirm_template_en-US.html
                transfer_workspace_owner_confirm_template_zh-CN.html
        tests/
            __init__.py
            artifact_tests/
                dependencies/
                    __init__.py
            integration_tests/
                .env.example
                .gitignore
                conftest.py
                __init__.py
                controllers/
                    console/
                        __init__.py
                        app/
                            test_description_validation.py
                            test_workflow_draft_variable.py
                            __init__.py
                factories/
                    test_storage_key_loader.py
                    __init__.py
                model_runtime/
                    __mock/
                        plugin_daemon.py
                        plugin_model.py
                plugin/
                    tools/
                        test_fetch_all_tools.py
                    __mock/
                        http.py
                services/
                    test_workflow_draft_variable_service.py
                    __init__.py
                storage/
                    test_clickzetta_volume.py
                tools/
                    __init__.py
                    api_tool/
                        test_api_tool.py
                        __init__.py
                    code/
                        __init__.py
                    __mock/
                        http.py
                    __mock_server/
                        openapi_todo.py
                utils/
                    child_class.py
                    lazy_load_class.py
                    parent_class.py
                    test_module_import_helper.py
                vdb/
                    test_vector_store.py
                    __init__.py
                    analyticdb/
                        test_analyticdb.py
                        __init__.py
                    baidu/
                        test_baidu.py
                        __init__.py
                    chroma/
                        test_chroma.py
                        __init__.py
                    clickzetta/
                        README.md
                        test_clickzetta.py
                        test_docker_integration.py
                    couchbase/
                        test_couchbase.py
                        __init__.py
                    elasticsearch/
                        test_elasticsearch.py
                        __init__.py
                    huawei/
                        test_huawei_cloud.py
                        __init__.py
                    lindorm/
                        test_lindorm.py
                        __init__.py
                    matrixone/
                        test_matrixone.py
                        __init__.py
                    milvus/
                        test_milvus.py
                        __init__.py
                    myscale/
                        test_myscale.py
                        __init__.py
                    oceanbase/
                        test_oceanbase.py
                        __init__.py
                    opengauss/
                        test_opengauss.py
                        __init__.py
                    opensearch/
                        test_opensearch.py
                        __init__.py
                    oracle/
                        test_oraclevector.py
                        __init__.py
                    pgvector/
                        test_pgvector.py
                        __init__.py
                    pgvecto_rs/
                        test_pgvecto_rs.py
                        __init__.py
                    pyvastbase/
                        test_vastbase_vector.py
                        __init__.py
                    qdrant/
                        test_qdrant.py
                        __init__.py
                    tablestore/
                        test_tablestore.py
                        __init__.py
                    tcvectordb/
                        test_tencent.py
                        __init__.py
                    tidb_vector/
                        check_tiflash_ready.py
                        test_tidb_vector.py
                        __init__.py
                    upstash/
                        test_upstash_vector.py
                        __init__.py
                    vikingdb/
                        test_vikingdb.py
                        __init__.py
                    weaviate/
                        test_weaviate.py
                        __init__.py
                    __mock/
                        baiduvectordb.py
                        huaweicloudvectordb.py
                        tcvectordb.py
                        upstashvectordb.py
                        vikingdb.py
                        __init__.py
                workflow/
                    test_sync_workflow.py
                    __init__.py
                    nodes/
                        test_code.py
                        test_http.py
                        test_llm.py
                        test_parameter_extractor.py
                        test_template_transform.py
                        test_tool.py
                        __init__.py
                        code_executor/
                            test_code_executor.py
                            test_code_javascript.py
                            test_code_jinja2.py
                            test_code_python3.py
                            __init__.py
                        __mock/
                            code_executor.py
                            http.py
                            model.py
            test_containers_integration_tests/
                conftest.py
                __init__.py
                factories/
                    test_storage_key_loader.py
                    __init__.py
                services/
                    test_account_service.py
                    test_annotation_service.py
                    test_api_based_extension_service.py
                    test_app_dsl_service.py
                    test_app_service.py
                    test_message_service.py
                    test_workflow_draft_variable_service.py
                    __init__.py
                workflow/
                    __init__.py
                    nodes/
                        __init__.py
                        code_executor/
                            test_code_executor.py
                            test_code_javascript.py
                            test_code_jinja2.py
                            test_code_python3.py
                            test_utils.py
                            __init__.py
            unit_tests/
                .gitignore
                conftest.py
                __init__.py
                configs/
                    test_dify_config.py
                controllers/
                    test_compare_versions.py
                    console/
                        test_files_security.py
                        test_wraps.py
                        app/
                            test_description_validation.py
                            workflow_draft_variables_test.py
                        auth/
                            test_oauth.py
                    service_api/
                        app/
                            test_file_preview.py
                core/
                    test_file.py
                    test_model_manager.py
                    test_provider_manager.py
                    __init__.py
                    agent/
                        output_parser/
                            test_cot_output_parser.py
                    app/
                        apps/
                            test_base_app_generator.py
                            advanced_chat/
                                test_app_runner_conversation_variables.py
                            common/
                                test_workflow_response_converter.py
                        app_config/
                            features/
                                file_upload/
                                    test_manager.py
                        features/
                            rate_limiting/
                                conftest.py
                                test_rate_limit.py
                    file/
                        test_models.py
                    helper/
                        test_encrypter.py
                        test_ssrf_proxy.py
                        test_trace_id_helper.py
                        __init__.py
                    mcp/
                        client/
                            test_session.py
                            test_sse.py
                            test_streamable_http.py
                    model_runtime/
                        __init__.py
                        __base/
                            test_increase_tool_call.py
                            __init__.py
                    ops/
                        test_config_entity.py
                        test_utils.py
                        __init__.py
                    prompt/
                        test_advanced_prompt_transform.py
                        test_agent_history_prompt_transform.py
                        test_extract_thread_messages.py
                        test_prompt_message.py
                        test_prompt_transform.py
                        test_simple_prompt_transform.py
                        __init__.py
                    rag/
                        __init__.py
                        datasource/
                            __init__.py
                            vdb/
                                __init__.py
                                milvus/
                                    test_milvus.py
                        extractor/
                            test_markdown_extractor.py
                            test_notion_extractor.py
                            __init__.py
                            firecrawl/
                                test_firecrawl.py
                                __init__.py
                    repositories/
                        test_factory.py
                        __init__.py
                    tools/
                        test_tool_parameter_type.py
                        utils/
                            test_parser.py
                            __init__.py
                        workflow_as_tool/
                            test_tool.py
                            __init__.py
                    variables/
                        test_segment.py
                        test_segment_type.py
                        test_variables.py
                    workflow/
                        test_system_variable.py
                        test_variable_pool.py
                        test_workflow_cycle_manager.py
                        __init__.py
                        graph_engine/
                            test_graph.py
                            test_graph_engine.py
                            __init__.py
                            entities/
                                test_graph_runtime_state.py
                                test_node_run_state.py
                        nodes/
                            test_answer.py
                            test_continue_on_error.py
                            test_document_extractor_node.py
                            test_if_else.py
                            test_list_operator.py
                            test_question_classifier_node.py
                            test_retry.py
                            __init__.py
                            answer/
                                test_answer.py
                                test_answer_stream_generate_router.py
                                test_answer_stream_processor.py
                                __init__.py
                            base/
                                test_base_node.py
                            http_request/
                                test_entities.py
                                test_http_request_executor.py
                                test_http_request_node.py
                            iteration/
                                test_iteration.py
                                __init__.py
                            llm/
                                test_file_saver.py
                                test_node.py
                                test_scenarios.py
                            tool/
                                test_tool_node.py
                                __init__.py
                            variable_assigner/
                                v1/
                                    test_variable_assigner_v1.py
                                v2/
                                    test_helpers.py
                                    test_variable_assigner_v2.py
                                    __init__.py
                        utils/
                            test_variable_template_parser.py
                extensions/
                    test_ext_request_logging.py
                    test_redis.py
                factories/
                    test_build_from_mapping.py
                    test_variable_factory.py
                libs/
                    test_datetime_utils.py
                    test_email.py
                    test_email_i18n.py
                    test_flask_utils.py
                    test_helper.py
                    test_login.py
                    test_oauth_clients.py
                    test_pandas.py
                    test_passport.py
                    test_password.py
                    test_rsa.py
                    test_uuid_utils.py
                    test_yarl.py
                models/
                    test_account.py
                    test_conversation_variable.py
                    test_types_enum_text.py
                    test_workflow.py
                    __init__.py
                oss/
                    __init__.py
                    aliyun_oss/
                        aliyun_oss/
                            test_aliyun_oss.py
                            __init__.py
                    opendal/
                        test_opendal.py
                        __init__.py
                    tencent_cos/
                        test_tencent_cos.py
                        __init__.py
                    volcengine_tos/
                        test_volcengine_tos.py
                        __init__.py
                    __mock/
                        aliyun_oss.py
                        base.py
                        local.py
                        tencent_cos.py
                        volcengine_tos.py
                        __init__.py
                repositories/
                    __init__.py
                    workflow_node_execution/
                        test_sqlalchemy_repository.py
                        __init__.py
                services/
                    services_test_help.py
                    test_account_service.py
                    test_clear_free_plan_tenant_expired_logs.py
                    test_conversation_service.py
                    test_dataset_permission.py
                    test_dataset_service_batch_update_document_status.py
                    test_dataset_service_update_dataset.py
                    test_metadata_bug_complete.py
                    test_metadata_nullable_bug.py
                    __init__.py
                    auth/
                        test_api_key_auth_base.py
                        test_api_key_auth_factory.py
                        test_api_key_auth_service.py
                        test_auth_integration.py
                        test_auth_type.py
                        test_firecrawl_auth.py
                        test_jina_auth.py
                        test_watercrawl_auth.py
                        __init__.py
                    tools/
                        test_tools_transform_service.py
                        __init__.py
                    workflow/
                        test_workflow_converter.py
                        test_workflow_deletion.py
                        test_workflow_draft_variable_service.py
                        test_workflow_node_execution_service_repository.py
                        test_workflow_service.py
                        __init__.py
                utils/
                    test_text_processing.py
                    __init__.py
                    http_parser/
                        test_oauth_convert_request_to_raw_data.py
                    oauth_encryption/
                        test_system_oauth_encryption.py
                    position_helper/
                        test_position_helper.py
                    structured_output_parser/
                        test_structured_output_parser.py
                        __init__.py
                    yaml/
                        test_yaml_utils.py
                        __init__.py
    dev/
        mypy-check
        reformat
        start-api
        start-worker
        sync-uv
        update-uv
        pytest/
            pytest_all_tests.sh
            pytest_artifacts.sh
            pytest_config_tests.py
            pytest_model_runtime.sh
            pytest_testcontainers.sh
            pytest_tools.sh
            pytest_unit_tests.sh
            pytest_vdb.sh
            pytest_workflow.sh
    docker/
        .env
        docker-compose-template.yaml
        docker-compose.middleware.yaml
        docker-compose.png
        docker-compose.yaml
        generate_docker_compose
        middleware.env.example
        README.md
        certbot/
            docker-entrypoint.sh
            README.md
            update-cert.template.txt
        couchbase-server/
            Dockerfile
            init-cbserver.sh
        elasticsearch/
            docker-entrypoint.sh
        nginx/
            docker-entrypoint.sh
            https.conf.template
            nginx.conf.template
            proxy.conf.template
            conf.d/
                default.conf.template
            ssl/
                .gitkeep
        pgvector/
            docker-entrypoint.sh
        ssrf_proxy/
            docker-entrypoint.sh
            squid.conf.template
        startupscripts/
            init.sh
            init_user.script
        tidb/
            docker-compose.yaml
            config/
                pd.toml
                tiflash-learner.toml
                tiflash.toml
        volumes/
            myscale/
                config/
                    users.d/
                        custom_users_config.xml
            oceanbase/
                init.d/
                    vec_memory.sql
            opensearch/
                opensearch_dashboards.yml
            sandbox/
                conf/
                    config.yaml
                    config.yaml.example
                dependencies/
                    python-requirements.txt
    images/
        describe.png
        GitHub_README_if.png
        models.png
    node_modules/
        .package-lock.json
        .bin/
            tsc
            tsc.cmd
            tsc.ps1
            tsserver
            tsserver.cmd
            tsserver.ps1
        typescript/
            LICENSE.txt
            package.json
            README.md
            SECURITY.md
            ThirdPartyNoticeText.txt
            bin/
                tsc
                tsserver
            lib/
                lib.d.ts
                lib.decorators.d.ts
                lib.decorators.legacy.d.ts
                lib.dom.asynciterable.d.ts
                lib.dom.d.ts
                lib.dom.iterable.d.ts
                lib.es2015.collection.d.ts
                lib.es2015.core.d.ts
                lib.es2015.d.ts
                lib.es2015.generator.d.ts
                lib.es2015.iterable.d.ts
                lib.es2015.promise.d.ts
                lib.es2015.proxy.d.ts
                lib.es2015.reflect.d.ts
                lib.es2015.symbol.d.ts
                lib.es2015.symbol.wellknown.d.ts
                lib.es2016.array.include.d.ts
                lib.es2016.d.ts
                lib.es2016.full.d.ts
                lib.es2016.intl.d.ts
                lib.es2017.arraybuffer.d.ts
                lib.es2017.d.ts
                lib.es2017.date.d.ts
                lib.es2017.full.d.ts
                lib.es2017.intl.d.ts
                lib.es2017.object.d.ts
                lib.es2017.sharedmemory.d.ts
                lib.es2017.string.d.ts
                lib.es2017.typedarrays.d.ts
                lib.es2018.asyncgenerator.d.ts
                lib.es2018.asynciterable.d.ts
                lib.es2018.d.ts
                lib.es2018.full.d.ts
                lib.es2018.intl.d.ts
                lib.es2018.promise.d.ts
                lib.es2018.regexp.d.ts
                lib.es2019.array.d.ts
                lib.es2019.d.ts
                lib.es2019.full.d.ts
                lib.es2019.intl.d.ts
                lib.es2019.object.d.ts
                lib.es2019.string.d.ts
                lib.es2019.symbol.d.ts
                lib.es2020.bigint.d.ts
                lib.es2020.d.ts
                lib.es2020.date.d.ts
                lib.es2020.full.d.ts
                lib.es2020.intl.d.ts
                lib.es2020.number.d.ts
                lib.es2020.promise.d.ts
                lib.es2020.sharedmemory.d.ts
                lib.es2020.string.d.ts
                lib.es2020.symbol.wellknown.d.ts
                lib.es2021.d.ts
                lib.es2021.full.d.ts
                lib.es2021.intl.d.ts
                lib.es2021.promise.d.ts
                lib.es2021.string.d.ts
                lib.es2021.weakref.d.ts
                lib.es2022.array.d.ts
                lib.es2022.d.ts
                lib.es2022.error.d.ts
                lib.es2022.full.d.ts
                lib.es2022.intl.d.ts
                lib.es2022.object.d.ts
                lib.es2022.regexp.d.ts
                lib.es2022.string.d.ts
                lib.es2023.array.d.ts
                lib.es2023.collection.d.ts
                lib.es2023.d.ts
                lib.es2023.full.d.ts
                lib.es2023.intl.d.ts
                lib.es2024.arraybuffer.d.ts
                lib.es2024.collection.d.ts
                lib.es2024.d.ts
                lib.es2024.full.d.ts
                lib.es2024.object.d.ts
                lib.es2024.promise.d.ts
                lib.es2024.regexp.d.ts
                lib.es2024.sharedmemory.d.ts
                lib.es2024.string.d.ts
                lib.es5.d.ts
                lib.es6.d.ts
                lib.esnext.array.d.ts
                lib.esnext.collection.d.ts
                lib.esnext.d.ts
                lib.esnext.decorators.d.ts
                lib.esnext.disposable.d.ts
                lib.esnext.error.d.ts
                lib.esnext.float16.d.ts
                lib.esnext.full.d.ts
                lib.esnext.intl.d.ts
                lib.esnext.iterator.d.ts
                lib.esnext.promise.d.ts
                lib.esnext.sharedmemory.d.ts
                lib.scripthost.d.ts
                lib.webworker.asynciterable.d.ts
                lib.webworker.d.ts
                lib.webworker.importscripts.d.ts
                lib.webworker.iterable.d.ts
                tsc.js
                tsserver.js
                tsserverlibrary.d.ts
                tsserverlibrary.js
                typescript.d.ts
                typescript.js
                typesMap.json
                typingsInstaller.js
                watchGuard.js
                _tsc.js
                _tsserver.js
                _typingsInstaller.js
                cs/
                    diagnosticMessages.generated.json
                de/
                    diagnosticMessages.generated.json
                es/
                    diagnosticMessages.generated.json
                fr/
                    diagnosticMessages.generated.json
                it/
                    diagnosticMessages.generated.json
                ja/
                    diagnosticMessages.generated.json
                ko/
                    diagnosticMessages.generated.json
                pl/
                    diagnosticMessages.generated.json
                pt-br/
                    diagnosticMessages.generated.json
                ru/
                    diagnosticMessages.generated.json
                tr/
                    diagnosticMessages.generated.json
                zh-cn/
                    diagnosticMessages.generated.json
                zh-tw/
                    diagnosticMessages.generated.json
    sdks/
        README.md
        nodejs-client/
            .gitignore
            index.d.ts
            index.js
            index.test.js
            package.json
            README.md
        php-client/
            .gitignore
            composer.json
            composer.lock
            dify-client.php
            README.md
        python-client/
            build.sh
            LICENSE
            MANIFEST.in
            README.md
            setup.py
            dify_client/
                client.py
                __init__.py
            tests/
                test_client.py
                __init__.py
    web/
        .dockerignore
        .env.example
        .gitignore
        Dockerfile
        eslint.config.mjs
        global.d.ts
        jest.config.ts
        jest.setup.ts
        middleware.ts
        next.config.js
        package.json
        pnpm-lock.yaml
        postcss.config.js
        README.md
        tailwind-common-config.ts
        tailwind.config.js
        tsconfig.json
        typography.js
        .husky/
            pre-commit
        .storybook/
            main.ts
            preview.tsx
            storybook.css
        .vscode/
            extensions.json
            settings.example.json
        app/
            layout.tsx
            page.module.css
            page.tsx
            routePrefixHandle.tsx
            (commonLayout)/
                layout.tsx
                app/
                    (appDetailLayout)/
                        layout.tsx
                        [appId]/
                            layout-main.tsx
                            layout.tsx
                            style.module.css
                            annotations/
                                page.tsx
                            configuration/
                                page.tsx
                            develop/
                                page.tsx
                            logs/
                                page.tsx
                            overview/
                                cardView.tsx
                                chartView.tsx
                                page.tsx
                                tracing/
                                    config-button.tsx
                                    config-popup.tsx
                                    config.ts
                                    field.tsx
                                    panel.tsx
                                    provider-config-modal.tsx
                                    provider-panel.tsx
                                    tracing-icon.tsx
                                    type.ts
                                    __tests__/
                                        svg-attribute-error-reproduction.spec.tsx
                            workflow/
                                page.tsx
                apps/
                    page.tsx
                datasets/
                    container.tsx
                    dataset-card.tsx
                    dataset-footer.tsx
                    datasets.tsx
                    doc.tsx
                    layout.tsx
                    new-dataset-card.tsx
                    page.tsx
                    store.ts
                    (datasetDetailLayout)/
                        layout.tsx
                        [datasetId]/
                            layout-main.tsx
                            layout.tsx
                            api/
                                page.tsx
                            documents/
                                page.tsx
                                style.module.css
                                create/
                                    page.tsx
                                [documentId]/
                                    page.tsx
                                    settings/
                                        page.tsx
                            hitTesting/
                                page.tsx
                            settings/
                                page.tsx
                    connect/
                        page.tsx
                    create/
                        page.tsx
                    template/
                        template.en.mdx
                        template.ja.mdx
                        template.zh.mdx
                education-apply/
                    page.tsx
                explore/
                    layout.tsx
                    apps/
                        page.tsx
                    installed/
                        [appId]/
                            page.tsx
                plugins/
                    page.tsx
                tools/
                    page.tsx
            (shareLayout)/
                layout.tsx
                chat/
                    [token]/
                        page.tsx
                chatbot/
                    [token]/
                        page.tsx
                completion/
                    [token]/
                        page.tsx
                components/
                    authenticated-layout.tsx
                    splash.tsx
                webapp-reset-password/
                    layout.tsx
                    page.tsx
                    check-code/
                        page.tsx
                    set-password/
                        page.tsx
                webapp-signin/
                    layout.tsx
                    normalForm.tsx
                    page.tsx
                    check-code/
                        page.tsx
                    components/
                        external-member-sso-auth.tsx
                        mail-and-code-auth.tsx
                        mail-and-password-auth.tsx
                        sso-auth.tsx
                workflow/
                    [token]/
                        page.tsx
            account/
                avatar.tsx
                header.tsx
                layout.tsx
                page.tsx
                account-page/
                    AvatarWithEdit.tsx
                    email-change-modal.tsx
                    index.tsx
                delete-account/
                    index.tsx
                    state.tsx
                    components/
                        check-email.tsx
                        feed-back.tsx
                        verify-email.tsx
            activate/
                activateForm.tsx
                page.tsx
            components/
                browser-initializer.tsx
                i18n-server.tsx
                i18n.tsx
                sentry-initializer.tsx
                swr-initializer.tsx
                with-i18n.tsx
                app/
                    store.ts
                    annotation/
                        batch-action.tsx
                        empty-element.tsx
                        filter.tsx
                        index.tsx
                        list.tsx
                        type.ts
                        add-annotation-modal/
                            index.tsx
                            edit-item/
                                index.tsx
                        batch-add-annotation-modal/
                            csv-downloader.tsx
                            csv-uploader.tsx
                            index.tsx
                        clear-all-annotations-confirm-modal/
                            index.tsx
                        edit-annotation-modal/
                            index.tsx
                            edit-item/
                                index.tsx
                        header-opts/
                            index.tsx
                        remove-annotation-confirm-modal/
                            index.tsx
                        view-annotation-modal/
                            hit-history-no-data.tsx
                            index.tsx
                    app-access-control/
                        access-control-dialog.tsx
                        access-control-item.tsx
                        add-member-or-group-pop.tsx
                        index.tsx
                        specific-groups-or-members.tsx
                    app-publisher/
                        features-wrapper.tsx
                        index.tsx
                        publish-with-multiple-model.tsx
                        suggested-action.tsx
                        version-info-modal.tsx
                    configuration/
                        index.tsx
                        style.module.css
                        base/
                            feature-panel/
                                index.tsx
                            group-name/
                                index.tsx
                            icons/
                                citation.tsx
                                more-like-this-icon.tsx
                                suggested-questions-after-answer-icon.tsx
                                remove-icon/
                                    index.tsx
                                    style.module.css
                            operation-btn/
                                index.tsx
                            var-highlight/
                                index.tsx
                                style.module.css
                            warning-mask/
                                cannot-query-dataset.tsx
                                formatting-changed.tsx
                                has-not-set-api.tsx
                                index.tsx
                                style.module.css
                        config/
                            agent-setting-button.tsx
                            config-audio.tsx
                            config-document.tsx
                            index.tsx
                            agent/
                                prompt-editor.tsx
                                agent-setting/
                                    index.tsx
                                    item-panel.tsx
                                agent-tools/
                                    index.tsx
                                    setting-built-in-tool.tsx
                            assistant-type-picker/
                                index.tsx
                            automatic/
                                automatic-btn.tsx
                                get-automatic-res.tsx
                                style.module.css
                            code-generator/
                                get-code-generator-res.tsx
                            feature/
                                use-feature.tsx
                        config-prompt/
                            advanced-prompt-input.tsx
                            index.tsx
                            message-type-selector.tsx
                            prompt-editor-height-resize-wrap.tsx
                            simple-prompt-input.tsx
                            style.module.css
                            confirm-add-var/
                                index.tsx
                            conversation-history/
                                edit-modal.tsx
                                history-panel.tsx
                        config-var/
                            index.tsx
                            input-type-icon.tsx
                            modal-foot.tsx
                            select-var-type.tsx
                            var-item.tsx
                            config-modal/
                                field.tsx
                                index.tsx
                            config-select/
                                index.spec.tsx
                                index.tsx
                            config-string/
                                index.tsx
                            select-type-item/
                                index.tsx
                        config-vision/
                            index.tsx
                            param-config-content.tsx
                            param-config.tsx
                        ctrl-btn-group/
                            index.tsx
                            style.module.css
                        dataset-config/
                            index.tsx
                            card-item/
                                index.tsx
                                item.tsx
                                style.module.css
                            context-var/
                                index.tsx
                                var-picker.tsx
                            params-config/
                                config-content.tsx
                                index.tsx
                                weighted-score.css
                                weighted-score.tsx
                            select-dataset/
                                index.tsx
                            settings-modal/
                                index.tsx
                            type-icon/
                                index.tsx
                        debug/
                            chat-user-input.tsx
                            hooks.tsx
                            index.tsx
                            types.ts
                            debug-with-multiple-model/
                                chat-item.tsx
                                context.tsx
                                debug-item.tsx
                                index.tsx
                                model-parameter-trigger.tsx
                                text-generation-item.tsx
                            debug-with-single-model/
                                index.tsx
                        features/
                            experience-enhance-group/
                                index.tsx
                                more-like-this/
                                    index.tsx
                        hooks/
                            use-advanced-prompt-config.ts
                        images/
                            prompt.svg
                        prompt-mode/
                            advanced-mode-waring.tsx
                        prompt-value-panel/
                            index.tsx
                            utils.ts
                        tools/
                            external-data-tool-modal.tsx
                            index.tsx
                    create-app-dialog/
                        index.tsx
                        app-card/
                            index.tsx
                        app-list/
                            index.tsx
                            sidebar.tsx
                    create-app-modal/
                        index.tsx
                    create-from-dsl-modal/
                        dsl-confirm-modal.tsx
                        index.tsx
                        uploader.tsx
                    duplicate-modal/
                        index.tsx
                    log/
                        filter.tsx
                        index.tsx
                        list.tsx
                        model-info.tsx
                        var-panel.tsx
                    log-annotation/
                        index.tsx
                    overview/
                        appCard.tsx
                        appChart.tsx
                        style.module.css
                        apikey-info-panel/
                            index.tsx
                        assets/
                            chromeplugin-install.svg
                            chromeplugin-option.svg
                            code-browser.svg
                            iframe-option.svg
                            refresh-hover.svg
                            refresh.svg
                            scripts-option.svg
                        customize/
                            index.tsx
                        embedded/
                            index.tsx
                            style.module.css
                        settings/
                            index.tsx
                    switch-app-modal/
                        index.tsx
                    text-generate/
                        item/
                            index.tsx
                            result-tab.tsx
                        saved-items/
                            index.tsx
                            no-data/
                                index.tsx
                    type-selector/
                        index.tsx
                    workflow-log/
                        detail.tsx
                        filter.tsx
                        index.tsx
                        list.tsx
                app-sidebar/
                    app-info.tsx
                    app-operations.tsx
                    app-sidebar-dropdown.tsx
                    basic.tsx
                    completion.png
                    dataset-info.tsx
                    expert.png
                    index.tsx
                    navLink.spec.tsx
                    navLink.tsx
                    sidebar-animation-issues.spec.tsx
                    style.module.css
                    text-squeeze-fix-verification.spec.tsx
                apps/
                    app-card.tsx
                    empty.tsx
                    footer.tsx
                    index.tsx
                    list.tsx
                    new-app-card.tsx
                    hooks/
                        use-apps-query-state.ts
                        use-dsl-drag-drop.ts
                base/
                    app-unavailable.tsx
                    badge.tsx
                    theme-selector.tsx
                    theme-switcher.tsx
                    action-button/
                        index.css
                        index.tsx
                    agent-log-modal/
                        detail.tsx
                        index.tsx
                        iteration.tsx
                        result.tsx
                        tool-call.tsx
                        tracing.tsx
                    answer-icon/
                        index.tsx
                    app-icon/
                        index.tsx
                        style.module.css
                    app-icon-picker/
                        hooks.tsx
                        ImageInput.tsx
                        index.tsx
                        style.module.css
                        utils.ts
                    audio-btn/
                        audio.player.manager.ts
                        audio.ts
                        index.tsx
                        style.module.css
                    audio-gallery/
                        AudioPlayer.tsx
                        index.tsx
                    auto-height-textarea/
                        common.tsx
                        index.tsx
                        style.module.scss
                    avatar/
                        index.tsx
                    badge/
                        index.css
                        index.tsx
                    block-input/
                        index.tsx
                    button/
                        add-button.tsx
                        index.css
                        index.spec.tsx
                        index.stories.tsx
                        index.tsx
                        sync-button.tsx
                    chat/
                        constants.ts
                        types.ts
                        utils.ts
                        chat/
                            check-input-forms-hooks.ts
                            content-switch.tsx
                            context.tsx
                            hooks.ts
                            index.tsx
                            question.stories.tsx
                            question.tsx
                            try-to-ask.tsx
                            type.ts
                            utils.ts
                            answer/
                                agent-content.tsx
                                basic-content.tsx
                                index.stories.tsx
                                index.tsx
                                more.tsx
                                operation.tsx
                                suggested-questions.tsx
                                tool-detail.tsx
                                workflow-process.tsx
                                __mocks__/
                                    markdownContent.ts
                                    markdownContentSVG.ts
                                    workflowProcess.ts
                            chat-input-area/
                                hooks.ts
                                index.tsx
                                operation.tsx
                            citation/
                                index.tsx
                                popup.tsx
                                progress-tooltip.tsx
                                tooltip.tsx
                            loading-anim/
                                index.tsx
                                style.module.css
                            log/
                                index.tsx
                            thought/
                                index.tsx
                                panel.tsx
                                tool.tsx
                        chat-with-history/
                            chat-wrapper.tsx
                            context.tsx
                            header-in-mobile.tsx
                            hooks.tsx
                            index.tsx
                            header/
                                index.tsx
                                mobile-operation-dropdown.tsx
                                operation.tsx
                            inputs-form/
                                content.tsx
                                index.tsx
                                view-form-dropdown.tsx
                            sidebar/
                                index.tsx
                                item.tsx
                                list.tsx
                                operation.tsx
                                rename-modal.tsx
                        embedded-chatbot/
                            chat-wrapper.tsx
                            context.tsx
                            hooks.tsx
                            index.tsx
                            utils.ts
                            header/
                                index.tsx
                            inputs-form/
                                content.tsx
                                index.tsx
                                view-form-dropdown.tsx
                            theme/
                                theme-context.ts
                                utils.ts
                        __tests__/
                            branchedTestMessages.json
                            legacyTestMessages.json
                            mixedTestMessages.json
                            multiRootNodesMessages.json
                            multiRootNodesWithLegacyTestMessages.json
                            partialMessages.json
                            realWorldMessages.json
                            utils.spec.ts
                            __snapshots__/
                                utils.spec.ts.snap
                    checkbox/
                        index.spec.tsx
                        index.tsx
                        assets/
                            indeterminate-icon.tsx
                    chip/
                        index.tsx
                    confirm/
                        index.tsx
                    content-dialog/
                        index.tsx
                    copy-btn/
                        index.tsx
                        style.module.css
                    copy-feedback/
                        index.tsx
                        style.module.css
                    copy-icon/
                        index.tsx
                    corner-label/
                        index.tsx
                    custom-icon/
                        index.tsx
                    date-and-time-picker/
                        hooks.ts
                        types.ts
                        calendar/
                            days-of-week.tsx
                            index.tsx
                            item.tsx
                        common/
                            option-list-item.tsx
                        date-picker/
                            footer.tsx
                            header.tsx
                            index.tsx
                        time-picker/
                            footer.tsx
                            header.tsx
                            index.tsx
                            options.tsx
                        utils/
                            dayjs.ts
                        year-and-month-picker/
                            footer.tsx
                            header.tsx
                            options.tsx
                    dialog/
                        index.tsx
                    divider/
                        index.spec.tsx
                        index.tsx
                        with-label.tsx
                    drawer/
                        index.tsx
                    drawer-plus/
                        index.tsx
                    dropdown/
                        index.tsx
                    emoji-picker/
                        index.tsx
                        Inner.tsx
                    features/
                        context.tsx
                        hooks.ts
                        index.tsx
                        store.ts
                        types.ts
                        new-feature-panel/
                            citation.tsx
                            dialog-wrapper.tsx
                            feature-bar.tsx
                            feature-card.tsx
                            follow-up.tsx
                            index.tsx
                            more-like-this.tsx
                            speech-to-text.tsx
                            annotation-reply/
                                annotation-ctrl-button.tsx
                                config-param-modal.tsx
                                config-param.tsx
                                index.tsx
                                type.ts
                                use-annotation-config.ts
                                score-slider/
                                    index.tsx
                                    base-slider/
                                        index.tsx
                                        style.module.css
                            conversation-opener/
                                index.tsx
                                modal.tsx
                            file-upload/
                                index.tsx
                                setting-content.tsx
                                setting-modal.tsx
                            image-upload/
                                index.tsx
                            moderation/
                                form-generation.tsx
                                index.tsx
                                moderation-content.tsx
                                moderation-setting-modal.tsx
                            text-to-speech/
                                index.tsx
                                param-config-content.tsx
                                voice-settings.tsx
                    file-icon/
                        index.tsx
                    file-uploader/
                        audio-preview.tsx
                        constants.ts
                        dynamic-pdf-preview.tsx
                        file-image-render.tsx
                        file-input.tsx
                        file-list-in-log.tsx
                        file-type-icon.tsx
                        hooks.ts
                        index.ts
                        pdf-preview.tsx
                        store.tsx
                        types.ts
                        utils.spec.ts
                        utils.ts
                        video-preview.tsx
                        file-from-link-or-local/
                            index.tsx
                        file-uploader-in-attachment/
                            file-item.tsx
                            index.tsx
                        file-uploader-in-chat-input/
                            file-image-item.tsx
                            file-item.tsx
                            file-list.tsx
                            index.tsx
                    float-popover-container/
                        index.tsx
                    float-right-container/
                        index.tsx
                    form/
                        index.tsx
                        types.ts
                        components/
                            label.spec.tsx
                            label.tsx
                            base/
                                base-field.tsx
                                base-form.tsx
                                index.tsx
                            field/
                                checkbox.tsx
                                number-input.tsx
                                options.tsx
                                select.tsx
                                text.tsx
                            form/
                                submit-button.tsx
                        form-scenarios/
                            auth/
                                index.tsx
                            demo/
                                contact-fields.tsx
                                index.tsx
                                shared-options.tsx
                                types.ts
                        hooks/
                            index.ts
                            use-check-validated.ts
                            use-get-form-values.ts
                            use-get-validators.ts
                        utils/
                            index.ts
                            secret-input/
                                index.ts
                    fullscreen-modal/
                        index.tsx
                    ga/
                        index.tsx
                    grid-mask/
                        Grid.svg
                        index.tsx
                        style.module.css
                    icons/
                        IconBase.spec.tsx
                        IconBase.tsx
                        script.mjs
                        utils.spec.ts
                        utils.ts
                        assets/
                            image/
                                llm/
                                    baichuan-text-cn.png
                                    minimax-text.png
                                    minimax.png
                                    tongyi-text-cn.png
                                    tongyi-text.png
                                    tongyi.png
                                    wxyy-text-cn.png
                                    wxyy-text.png
                                    wxyy.png
                            public/
                                avatar/
                                    robot.svg
                                    user.svg
                                billing/
                                    ar-cube-1.svg
                                    asterisk.svg
                                    aws-marketplace.svg
                                    azure.svg
                                    buildings.svg
                                    diamond.svg
                                    google-cloud.svg
                                    group-2.svg
                                    keyframe.svg
                                    sparkles-soft.svg
                                    sparkles.svg
                                common/
                                    d.svg
                                    diagonal-dividing-line.svg
                                    dify.svg
                                    gdpr.svg
                                    github.svg
                                    highlight.svg
                                    iso.svg
                                    line-3.svg
                                    lock.svg
                                    message-chat-square.svg
                                    multi-path-retrieval.svg
                                    n-to-1-retrieval.svg
                                    notion.svg
                                    soc2.svg
                                    sparkles-soft.svg
                                education/
                                    triangle.svg
                                files/
                                    csv.svg
                                    doc.svg
                                    docx.svg
                                    html.svg
                                    json.svg
                                    md.svg
                                    pdf.svg
                                    txt.svg
                                    unknown.svg
                                    xlsx.svg
                                    yaml.svg
                                knowledge/
                                    chunk.svg
                                    collapse.svg
                                    general-type.svg
                                    layout-right-2-line-mod.svg
                                    parent-child-type.svg
                                    selection-mod.svg
                                llm/
                                    Anthropic-dark.svg
                                    Anthropic-light.svg
                                    anthropic-text.svg
                                    anthropic.svg
                                    azure-openai-service-text.svg
                                    azure-openai-service.svg
                                    azureai-text.svg
                                    azureai.svg
                                    baichuan-text.svg
                                    baichuan.svg
                                    chatglm-text.svg
                                    chatglm.svg
                                    cohere-text.svg
                                    cohere.svg
                                    gpt-3.svg
                                    gpt-4.svg
                                    huggingface-text-hub.svg
                                    huggingface-text.svg
                                    huggingface.svg
                                    iflytek-spark-text-cn.svg
                                    iflytek-spark-text.svg
                                    iflytek-spark.svg
                                    jina-text.svg
                                    jina.svg
                                    localai-text.svg
                                    localai.svg
                                    microsoft.svg
                                    openai-black.svg
                                    openai-blue.svg
                                    openai-green.svg
                                    openai-teal.svg
                                    openai-text.svg
                                    openai-transparent.svg
                                    openai-violet.svg
                                    openai-yellow.svg
                                    openllm-text.svg
                                    openllm.svg
                                    replicate-text.svg
                                    replicate.svg
                                    xorbits-inference-text.svg
                                    xorbits-inference.svg
                                    zhipuai-text-cn.svg
                                    zhipuai-text.svg
                                    zhipuai.svg
                                model/
                                    checked.svg
                                other/
                                    default-tool-icon.svg
                                    Icon-3-dots.svg
                                    message-3-fill.svg
                                    row-struct.svg
                                plugins/
                                    google.svg
                                    partner-dark.svg
                                    partner-light.svg
                                    verified-dark.svg
                                    verified-light.svg
                                    web-reader.svg
                                    wikipedia.svg
                                thought/
                                    data-set.svg
                                    loading.svg
                                    search.svg
                                    thought-list.svg
                                    web-reader.svg
                                tracing/
                                    aliyun-icon-big.svg
                                    aliyun-icon.svg
                                    arize-icon-big.svg
                                    arize-icon.svg
                                    langfuse-icon-big.svg
                                    langfuse-icon.svg
                                    langsmith-icon-big.svg
                                    langsmith-icon.svg
                                    opik-icon-big.svg
                                    opik-icon.svg
                                    phoenix-icon-big.svg
                                    phoenix-icon.svg
                                    tracing-icon.svg
                                    weave-icon-big.svg
                                    weave-icon.svg
                            vender/
                                features/
                                    citations.svg
                                    content-moderation.svg
                                    document.svg
                                    folder-upload.svg
                                    love-message.svg
                                    message-fast.svg
                                    microphone-01.svg
                                    text-to-audio.svg
                                    virtual-assistant.svg
                                    vision.svg
                                line/
                                    alertsAndFeedback/
                                        alert-triangle.svg
                                        thumbs-down.svg
                                        thumbs-up.svg
                                    arrows/
                                        arrow-narrow-left.svg
                                        arrow-up-right.svg
                                        chevron-down-double.svg
                                        chevron-right.svg
                                        chevron-selector-vertical.svg
                                        refresh-ccw-01.svg
                                        refresh-cw-05.svg
                                        reverse-left.svg
                                    communication/
                                        ai-text.svg
                                        chat-bot-slim.svg
                                        chat-bot.svg
                                        cute-robot.svg
                                        message-check-remove.svg
                                        message-fast-plus.svg
                                    development/
                                        artificial-brain.svg
                                        bar-chart-square-02.svg
                                        brackets-x.svg
                                        code-browser.svg
                                        container.svg
                                        database-01.svg
                                        database-03.svg
                                        file-heart-02.svg
                                        git-branch-01.svg
                                        prompt-engineering.svg
                                        puzzle-piece-01.svg
                                        terminal-square.svg
                                        variable.svg
                                        webhooks.svg
                                    editor/
                                        align-left.svg
                                        bezier-curve-03.svg
                                        collapse.svg
                                        colors.svg
                                        image-indent-left.svg
                                        left-indent-02.svg
                                        letter-spacing-01.svg
                                        type-square.svg
                                    education/
                                        book-open-01.svg
                                    files/
                                        copy-check.svg
                                        copy.svg
                                        file-02.svg
                                        file-arrow-01.svg
                                        file-check-02.svg
                                        file-download-02.svg
                                        file-plus-01.svg
                                        file-plus-02.svg
                                        file-text.svg
                                        file-upload.svg
                                        folder.svg
                                    financeAndECommerce/
                                        balance.svg
                                        coins-stacked-01.svg
                                        gold-coin.svg
                                        receipt-list.svg
                                        tag-01.svg
                                        tag-03.svg
                                    general/
                                        at-sign.svg
                                        bookmark.svg
                                        check-done-01.svg
                                        check.svg
                                        checklist-square.svg
                                        dots-grid.svg
                                        edit-02.svg
                                        edit-04.svg
                                        edit-05.svg
                                        hash-02.svg
                                        info-circle.svg
                                        link-03.svg
                                        link-external-02.svg
                                        log-in-04.svg
                                        log-out-01.svg
                                        log-out-04.svg
                                        menu-01.svg
                                        pin-01.svg
                                        pin-02.svg
                                        plus-02.svg
                                        refresh.svg
                                        search-menu.svg
                                        settings-01.svg
                                        settings-04.svg
                                        target-04.svg
                                        upload-03.svg
                                        upload-cloud-01.svg
                                        x.svg
                                    images/
                                        image-plus.svg
                                    layout/
                                        align-left-01.svg
                                        align-right-01.svg
                                        grid-01.svg
                                        layout-grid-02.svg
                                    mapsAndTravel/
                                        globe-01.svg
                                        route.svg
                                    mediaAndDevices/
                                        microphone-01.svg
                                        play-circle.svg
                                        sliders-h.svg
                                        speaker.svg
                                        stop-circle.svg
                                        stop.svg
                                    others/
                                        apps-02.svg
                                        bubble-x.svg
                                        colors.svg
                                        drag-handle.svg
                                        env.svg
                                        exchange-02.svg
                                        file-code.svg
                                        global-variable.svg
                                        icon-3-dots.svg
                                        long-arrow-left.svg
                                        long-arrow-right.svg
                                        search-menu.svg
                                        tools.svg
                                    shapes/
                                        cube-outline.svg
                                    time/
                                        clock-fast-forward.svg
                                        clock-play-slim.svg
                                        clock-play.svg
                                        clock-refresh.svg
                                    users/
                                        user-01.svg
                                        users-01.svg
                                    weather/
                                        stars-02.svg
                                other/
                                    anthropic-text.svg
                                    generator.svg
                                    group.svg
                                    mcp.svg
                                    no-tool-placeholder.svg
                                    openai.svg
                                    replay-line.svg
                                plugin/
                                    box-sparkle-fill.svg
                                    left-corner.svg
                                solid/
                                    alertsAndFeedback/
                                        alert-triangle.svg
                                    arrows/
                                        chevron-down.svg
                                        high-priority.svg
                                    communication/
                                        ai-text.svg
                                        bubble-text-mod.svg
                                        chat-bot.svg
                                        cute-robot.svg
                                        edit-list.svg
                                        list-sparkle.svg
                                        logic.svg
                                        message-dots-circle.svg
                                        message-fast.svg
                                        message-heart-circle.svg
                                        message-smile-square.svg
                                        send-03.svg
                                    development/
                                        api-connection-mod.svg
                                        api-connection.svg
                                        bar-chart-square-02.svg
                                        container.svg
                                        database-02.svg
                                        database-03.svg
                                        file-heart-02.svg
                                        pattern-recognition.svg
                                        prompt-engineering.svg
                                        puzzle-piece-01.svg
                                        semantic.svg
                                        terminal-square.svg
                                        variable-02.svg
                                    editor/
                                        brush-01.svg
                                        citations.svg
                                        colors.svg
                                        paragraph.svg
                                        type-square.svg
                                    education/
                                        beaker-02.svg
                                        bubble-text.svg
                                        heart-02.svg
                                        unblur.svg
                                    files/
                                        file-05.svg
                                        file-search-02.svg
                                        file-zip.svg
                                        folder.svg
                                    FinanceAndECommerce/
                                        gold-coin.svg
                                        scales-02.svg
                                    general/
                                        answer-triangle.svg
                                        arrow-down-round-fill.svg
                                        check-circle.svg
                                        check-done-01.svg
                                        download-02.svg
                                        edit-03.svg
                                        edit-04.svg
                                        eye.svg
                                        github.svg
                                        message-clock-circle.svg
                                        plus-circle.svg
                                        question-triangle.svg
                                        search-md.svg
                                        target-04.svg
                                        tool-03.svg
                                        x-circle.svg
                                        zap-fast.svg
                                        zap-narrow.svg
                                    layout/
                                        grid-01.svg
                                    mapsAndTravel/
                                        globe-06.svg
                                        route.svg
                                    mediaAndDevices/
                                        audio-support-icon.svg
                                        document-support-icon.svg
                                        magic-box.svg
                                        magic-eyes.svg
                                        magic-wand.svg
                                        microphone-01.svg
                                        play.svg
                                        robot.svg
                                        sliders-02.svg
                                        speaker.svg
                                        stop-circle.svg
                                        video-support-icon.svg
                                    security/
                                        lock-01.svg
                                    shapes/
                                        corner.svg
                                        star-04.svg
                                        star-06.svg
                                    users/
                                        user-01.svg
                                        user-edit-02.svg
                                        users-01.svg
                                        users-plus.svg
                                system/
                                    auto-update-line.svg
                                workflow/
                                    agent.svg
                                    answer.svg
                                    assigner.svg
                                    code.svg
                                    docs-extractor.svg
                                    end.svg
                                    home.svg
                                    http.svg
                                    if-else.svg
                                    iteration-start.svg
                                    iteration.svg
                                    jinja.svg
                                    knowledge-retrieval.svg
                                    list-filter.svg
                                    llm.svg
                                    loop-end.svg
                                    loop.svg
                                    parameter-extractor.svg
                                    question-classifier.svg
                                    templating-transform.svg
                                    variable-x.svg
                                    window-cursor.svg
                        src/
                            image/
                                llm/
                                    BaichuanTextCn.module.css
                                    BaichuanTextCn.tsx
                                    index.ts
                                    Minimax.module.css
                                    Minimax.tsx
                                    MinimaxText.module.css
                                    MinimaxText.tsx
                                    Tongyi.module.css
                                    Tongyi.tsx
                                    TongyiText.module.css
                                    TongyiText.tsx
                                    TongyiTextCn.module.css
                                    TongyiTextCn.tsx
                                    Wxyy.module.css
                                    Wxyy.tsx
                                    WxyyText.module.css
                                    WxyyText.tsx
                                    WxyyTextCn.module.css
                                    WxyyTextCn.tsx
                            public/
                                avatar/
                                    index.ts
                                    Robot.json
                                    Robot.tsx
                                    User.json
                                    User.tsx
                                billing/
                                    ArCube1.json
                                    ArCube1.tsx
                                    Asterisk.json
                                    Asterisk.tsx
                                    AwsMarketplace.json
                                    AwsMarketplace.tsx
                                    Azure.json
                                    Azure.tsx
                                    Buildings.json
                                    Buildings.tsx
                                    Diamond.json
                                    Diamond.tsx
                                    GoogleCloud.json
                                    GoogleCloud.tsx
                                    Group2.json
                                    Group2.tsx
                                    index.ts
                                    Keyframe.json
                                    Keyframe.tsx
                                    Sparkles.json
                                    Sparkles.tsx
                                    SparklesSoft.json
                                    SparklesSoft.tsx
                                common/
                                    D.json
                                    D.tsx
                                    DiagonalDividingLine.json
                                    DiagonalDividingLine.tsx
                                    Dify.json
                                    Dify.tsx
                                    Gdpr.json
                                    Gdpr.tsx
                                    Github.json
                                    Github.tsx
                                    Highlight.json
                                    Highlight.tsx
                                    index.ts
                                    Iso.json
                                    Iso.tsx
                                    Line3.json
                                    Line3.tsx
                                    Lock.json
                                    Lock.tsx
                                    MessageChatSquare.json
                                    MessageChatSquare.tsx
                                    MultiPathRetrieval.json
                                    MultiPathRetrieval.tsx
                                    Notion.json
                                    Notion.tsx
                                    NTo1Retrieval.json
                                    NTo1Retrieval.tsx
                                    Soc2.json
                                    Soc2.tsx
                                    SparklesSoft.json
                                    SparklesSoft.tsx
                                education/
                                    index.ts
                                    Triangle.json
                                    Triangle.tsx
                                files/
                                    Csv.json
                                    Csv.tsx
                                    Doc.json
                                    Doc.tsx
                                    Docx.json
                                    Docx.tsx
                                    Html.json
                                    Html.tsx
                                    index.ts
                                    Json.json
                                    Json.tsx
                                    Md.json
                                    Md.tsx
                                    Pdf.json
                                    Pdf.tsx
                                    Txt.json
                                    Txt.tsx
                                    Unknown.json
                                    Unknown.tsx
                                    Xlsx.json
                                    Xlsx.tsx
                                    Yaml.json
                                    Yaml.tsx
                                knowledge/
                                    Chunk.json
                                    Chunk.tsx
                                    Collapse.json
                                    Collapse.tsx
                                    GeneralType.json
                                    GeneralType.tsx
                                    index.ts
                                    LayoutRight2LineMod.json
                                    LayoutRight2LineMod.tsx
                                    ParentChildType.json
                                    ParentChildType.tsx
                                    SelectionMod.json
                                    SelectionMod.tsx
                                llm/
                                    Anthropic.json
                                    Anthropic.tsx
                                    AnthropicDark.json
                                    AnthropicDark.tsx
                                    AnthropicLight.json
                                    AnthropicLight.tsx
                                    AnthropicText.json
                                    AnthropicText.tsx
                                    Azureai.json
                                    Azureai.tsx
                                    AzureaiText.json
                                    AzureaiText.tsx
                                    AzureOpenaiService.json
                                    AzureOpenaiService.tsx
                                    AzureOpenaiServiceText.json
                                    AzureOpenaiServiceText.tsx
                                    Baichuan.json
                                    Baichuan.tsx
                                    BaichuanText.json
                                    BaichuanText.tsx
                                    Chatglm.json
                                    Chatglm.tsx
                                    ChatglmText.json
                                    ChatglmText.tsx
                                    Cohere.json
                                    Cohere.tsx
                                    CohereText.json
                                    CohereText.tsx
                                    Gpt3.json
                                    Gpt3.tsx
                                    Gpt4.json
                                    Gpt4.tsx
                                    Huggingface.json
                                    Huggingface.tsx
                                    HuggingfaceText.json
                                    HuggingfaceText.tsx
                                    HuggingfaceTextHub.json
                                    HuggingfaceTextHub.tsx
                                    IflytekSpark.json
                                    IflytekSpark.tsx
                                    IflytekSparkText.json
                                    IflytekSparkText.tsx
                                    IflytekSparkTextCn.json
                                    IflytekSparkTextCn.tsx
                                    index.ts
                                    Jina.json
                                    Jina.tsx
                                    JinaText.json
                                    JinaText.tsx
                                    Localai.json
                                    Localai.tsx
                                    LocalaiText.json
                                    LocalaiText.tsx
                                    Microsoft.json
                                    Microsoft.tsx
                                    OpenaiBlack.json
                                    OpenaiBlack.tsx
                                    OpenaiBlue.json
                                    OpenaiBlue.tsx
                                    OpenaiGreen.json
                                    OpenaiGreen.tsx
                                    OpenaiTeal.json
                                    OpenaiTeal.tsx
                                    OpenaiText.json
                                    OpenaiText.tsx
                                    OpenaiTransparent.json
                                    OpenaiTransparent.tsx
                                    OpenaiViolet.json
                                    OpenaiViolet.tsx
                                    OpenaiYellow.json
                                    OpenaiYellow.tsx
                                    Openllm.json
                                    Openllm.tsx
                                    OpenllmText.json
                                    OpenllmText.tsx
                                    Replicate.json
                                    Replicate.tsx
                                    ReplicateText.json
                                    ReplicateText.tsx
                                    XorbitsInference.json
                                    XorbitsInference.tsx
                                    XorbitsInferenceText.json
                                    XorbitsInferenceText.tsx
                                    Zhipuai.json
                                    Zhipuai.tsx
                                    ZhipuaiText.json
                                    ZhipuaiText.tsx
                                    ZhipuaiTextCn.json
                                    ZhipuaiTextCn.tsx
                                model/
                                    Checked.json
                                    Checked.tsx
                                    index.ts
                                other/
                                    DefaultToolIcon.json
                                    DefaultToolIcon.tsx
                                    Icon3Dots.json
                                    Icon3Dots.tsx
                                    index.ts
                                    Message3Fill.json
                                    Message3Fill.tsx
                                    RowStruct.json
                                    RowStruct.tsx
                                plugins/
                                    Google.json
                                    Google.tsx
                                    index.ts
                                    PartnerDark.json
                                    PartnerDark.tsx
                                    PartnerLight.json
                                    PartnerLight.tsx
                                    VerifiedDark.json
                                    VerifiedDark.tsx
                                    VerifiedLight.json
                                    VerifiedLight.tsx
                                    WebReader.json
                                    WebReader.tsx
                                    Wikipedia.json
                                    Wikipedia.tsx
                                thought/
                                    DataSet.json
                                    DataSet.tsx
                                    index.ts
                                    Loading.json
                                    Loading.tsx
                                    Search.json
                                    Search.tsx
                                    ThoughtList.json
                                    ThoughtList.tsx
                                    WebReader.json
                                    WebReader.tsx
                                tracing/
                                    AliyunIcon.json
                                    AliyunIcon.tsx
                                    AliyunIconBig.json
                                    AliyunIconBig.tsx
                                    ArizeIcon.json
                                    ArizeIcon.tsx
                                    ArizeIconBig.json
                                    ArizeIconBig.tsx
                                    index.ts
                                    LangfuseIcon.json
                                    LangfuseIcon.tsx
                                    LangfuseIconBig.json
                                    LangfuseIconBig.tsx
                                    LangsmithIcon.json
                                    LangsmithIcon.tsx
                                    LangsmithIconBig.json
                                    LangsmithIconBig.tsx
                                    OpikIcon.json
                                    OpikIcon.tsx
                                    OpikIconBig.json
                                    OpikIconBig.tsx
                                    PhoenixIcon.json
                                    PhoenixIcon.tsx
                                    PhoenixIconBig.json
                                    PhoenixIconBig.tsx
                                    TracingIcon.json
                                    TracingIcon.tsx
                                    WeaveIcon.json
                                    WeaveIcon.tsx
                                    WeaveIconBig.json
                                    WeaveIconBig.tsx
                            vender/
                                features/
                                    Citations.json
                                    Citations.tsx
                                    ContentModeration.json
                                    ContentModeration.tsx
                                    Document.json
                                    Document.tsx
                                    FolderUpload.json
                                    FolderUpload.tsx
                                    index.ts
                                    LoveMessage.json
                                    LoveMessage.tsx
                                    MessageFast.json
                                    MessageFast.tsx
                                    Microphone01.json
                                    Microphone01.tsx
                                    TextToAudio.json
                                    TextToAudio.tsx
                                    VirtualAssistant.json
                                    VirtualAssistant.tsx
                                    Vision.json
                                    Vision.tsx
                                line/
                                    alertsAndFeedback/
                                        AlertTriangle.json
                                        AlertTriangle.tsx
                                        index.ts
                                        ThumbsDown.json
                                        ThumbsDown.tsx
                                        ThumbsUp.json
                                        ThumbsUp.tsx
                                    arrows/
                                        ArrowNarrowLeft.json
                                        ArrowNarrowLeft.tsx
                                        ArrowUpRight.json
                                        ArrowUpRight.tsx
                                        ChevronDownDouble.json
                                        ChevronDownDouble.tsx
                                        ChevronRight.json
                                        ChevronRight.tsx
                                        ChevronSelectorVertical.json
                                        ChevronSelectorVertical.tsx
                                        index.ts
                                        RefreshCcw01.json
                                        RefreshCcw01.tsx
                                        RefreshCw05.json
                                        RefreshCw05.tsx
                                        ReverseLeft.json
                                        ReverseLeft.tsx
                                    communication/
                                        AiText.json
                                        AiText.tsx
                                        ChatBot.json
                                        ChatBot.tsx
                                        ChatBotSlim.json
                                        ChatBotSlim.tsx
                                        CuteRobot.json
                                        CuteRobot.tsx
                                        index.ts
                                        MessageCheckRemove.json
                                        MessageCheckRemove.tsx
                                        MessageFastPlus.json
                                        MessageFastPlus.tsx
                                    development/
                                        ArtificialBrain.json
                                        ArtificialBrain.tsx
                                        BarChartSquare02.json
                                        BarChartSquare02.tsx
                                        BracketsX.json
                                        BracketsX.tsx
                                        CodeBrowser.json
                                        CodeBrowser.tsx
                                        Container.json
                                        Container.tsx
                                        Database01.json
                                        Database01.tsx
                                        Database03.json
                                        Database03.tsx
                                        FileHeart02.json
                                        FileHeart02.tsx
                                        GitBranch01.json
                                        GitBranch01.tsx
                                        index.ts
                                        PromptEngineering.json
                                        PromptEngineering.tsx
                                        PuzzlePiece01.json
                                        PuzzlePiece01.tsx
                                        TerminalSquare.json
                                        TerminalSquare.tsx
                                        Variable.json
                                        Variable.tsx
                                        Webhooks.json
                                        Webhooks.tsx
                                    editor/
                                        AlignLeft.json
                                        AlignLeft.tsx
                                        BezierCurve03.json
                                        BezierCurve03.tsx
                                        Collapse.json
                                        Collapse.tsx
                                        Colors.json
                                        Colors.tsx
                                        ImageIndentLeft.json
                                        ImageIndentLeft.tsx
                                        index.ts
                                        LeftIndent02.json
                                        LeftIndent02.tsx
                                        LetterSpacing01.json
                                        LetterSpacing01.tsx
                                        TypeSquare.json
                                        TypeSquare.tsx
                                    education/
                                        BookOpen01.json
                                        BookOpen01.tsx
                                        index.ts
                                    files/
                                        Copy.json
                                        Copy.tsx
                                        CopyCheck.json
                                        CopyCheck.tsx
                                        File02.json
                                        File02.tsx
                                        FileArrow01.json
                                        FileArrow01.tsx
                                        FileCheck02.json
                                        FileCheck02.tsx
                                        FileDownload02.json
                                        FileDownload02.tsx
                                        FilePlus01.json
                                        FilePlus01.tsx
                                        FilePlus02.json
                                        FilePlus02.tsx
                                        FileText.json
                                        FileText.tsx
                                        FileUpload.json
                                        FileUpload.tsx
                                        Folder.json
                                        Folder.tsx
                                        index.ts
                                    financeAndECommerce/
                                        Balance.json
                                        Balance.tsx
                                        CoinsStacked01.json
                                        CoinsStacked01.tsx
                                        GoldCoin.json
                                        GoldCoin.tsx
                                        index.ts
                                        ReceiptList.json
                                        ReceiptList.tsx
                                        Tag01.json
                                        Tag01.tsx
                                        Tag03.json
                                        Tag03.tsx
                                    general/
                                        AtSign.json
                                        AtSign.tsx
                                        Bookmark.json
                                        Bookmark.tsx
                                        Check.json
                                        Check.tsx
                                        CheckDone01.json
                                        CheckDone01.tsx
                                        ChecklistSquare.json
                                        ChecklistSquare.tsx
                                        DotsGrid.json
                                        DotsGrid.tsx
                                        Edit02.json
                                        Edit02.tsx
                                        Edit04.json
                                        Edit04.tsx
                                        Edit05.json
                                        Edit05.tsx
                                        Hash02.json
                                        Hash02.tsx
                                        index.ts
                                        InfoCircle.json
                                        InfoCircle.tsx
                                        Link03.json
                                        Link03.tsx
                                        LinkExternal02.json
                                        LinkExternal02.tsx
                                        LogIn04.json
                                        LogIn04.tsx
                                        LogOut01.json
                                        LogOut01.tsx
                                        LogOut04.json
                                        LogOut04.tsx
                                        Menu01.json
                                        Menu01.tsx
                                        Pin01.json
                                        Pin01.tsx
                                        Pin02.json
                                        Pin02.tsx
                                        Plus02.json
                                        Plus02.tsx
                                        Refresh.json
                                        Refresh.tsx
                                        SearchMenu.json
                                        SearchMenu.tsx
                                        Settings01.json
                                        Settings01.tsx
                                        Settings04.json
                                        Settings04.tsx
                                        Target04.json
                                        Target04.tsx
                                        Upload03.json
                                        Upload03.tsx
                                        UploadCloud01.json
                                        UploadCloud01.tsx
                                        X.json
                                        X.tsx
                                    images/
                                        ImagePlus.json
                                        ImagePlus.tsx
                                        index.ts
                                    layout/
                                        AlignLeft01.json
                                        AlignLeft01.tsx
                                        AlignRight01.json
                                        AlignRight01.tsx
                                        Grid01.json
                                        Grid01.tsx
                                        index.ts
                                        LayoutGrid02.json
                                        LayoutGrid02.tsx
                                    mapsAndTravel/
                                        Globe01.json
                                        Globe01.tsx
                                        index.ts
                                        Route.json
                                        Route.tsx
                                    mediaAndDevices/
                                        index.ts
                                        Microphone01.json
                                        Microphone01.tsx
                                        PlayCircle.json
                                        PlayCircle.tsx
                                        SlidersH.json
                                        SlidersH.tsx
                                        Speaker.json
                                        Speaker.tsx
                                        Stop.json
                                        Stop.tsx
                                        StopCircle.json
                                        StopCircle.tsx
                                    others/
                                        Apps02.json
                                        Apps02.tsx
                                        BubbleX.json
                                        BubbleX.tsx
                                        Colors.json
                                        Colors.tsx
                                        DragHandle.json
                                        DragHandle.tsx
                                        Env.json
                                        Env.tsx
                                        Exchange02.json
                                        Exchange02.tsx
                                        FileCode.json
                                        FileCode.tsx
                                        GlobalVariable.json
                                        GlobalVariable.tsx
                                        Icon3Dots.json
                                        Icon3Dots.tsx
                                        index.ts
                                        LongArrowLeft.json
                                        LongArrowLeft.tsx
                                        LongArrowRight.json
                                        LongArrowRight.tsx
                                        SearchMenu.json
                                        SearchMenu.tsx
                                        Tools.json
                                        Tools.tsx
                                    shapes/
                                        CubeOutline.json
                                        CubeOutline.tsx
                                        index.ts
                                    time/
                                        ClockFastForward.json
                                        ClockFastForward.tsx
                                        ClockPlay.json
                                        ClockPlay.tsx
                                        ClockPlaySlim.json
                                        ClockPlaySlim.tsx
                                        ClockRefresh.json
                                        ClockRefresh.tsx
                                        index.ts
                                    users/
                                        index.ts
                                        User01.json
                                        User01.tsx
                                        Users01.json
                                        Users01.tsx
                                    weather/
                                        index.ts
                                        Stars02.json
                                        Stars02.tsx
                                other/
                                    AnthropicText.json
                                    AnthropicText.tsx
                                    Generator.json
                                    Generator.tsx
                                    Group.json
                                    Group.tsx
                                    index.ts
                                    Mcp.json
                                    Mcp.tsx
                                    NoToolPlaceholder.json
                                    NoToolPlaceholder.tsx
                                    Openai.json
                                    Openai.tsx
                                    ReplayLine.json
                                    ReplayLine.tsx
                                plugin/
                                    BoxSparkleFill.json
                                    BoxSparkleFill.tsx
                                    index.ts
                                    LeftCorner.json
                                    LeftCorner.tsx
                                solid/
                                    alertsAndFeedback/
                                        AlertTriangle.json
                                        AlertTriangle.tsx
                                        index.ts
                                    arrows/
                                        ChevronDown.json
                                        ChevronDown.tsx
                                        HighPriority.json
                                        HighPriority.tsx
                                        index.ts
                                    communication/
                                        AiText.json
                                        AiText.tsx
                                        BubbleTextMod.json
                                        BubbleTextMod.tsx
                                        ChatBot.json
                                        ChatBot.tsx
                                        CuteRobot.json
                                        CuteRobot.tsx
                                        EditList.json
                                        EditList.tsx
                                        index.ts
                                        ListSparkle.json
                                        ListSparkle.tsx
                                        Logic.json
                                        Logic.tsx
                                        MessageDotsCircle.json
                                        MessageDotsCircle.tsx
                                        MessageFast.json
                                        MessageFast.tsx
                                        MessageHeartCircle.json
                                        MessageHeartCircle.tsx
                                        MessageSmileSquare.json
                                        MessageSmileSquare.tsx
                                        Send03.json
                                        Send03.tsx
                                    development/
                                        ApiConnection.json
                                        ApiConnection.tsx
                                        ApiConnectionMod.json
                                        ApiConnectionMod.tsx
                                        BarChartSquare02.json
                                        BarChartSquare02.tsx
                                        Container.json
                                        Container.tsx
                                        Database02.json
                                        Database02.tsx
                                        Database03.json
                                        Database03.tsx
                                        FileHeart02.json
                                        FileHeart02.tsx
                                        index.ts
                                        PatternRecognition.json
                                        PatternRecognition.tsx
                                        PromptEngineering.json
                                        PromptEngineering.tsx
                                        PuzzlePiece01.json
                                        PuzzlePiece01.tsx
                                        Semantic.json
                                        Semantic.tsx
                                        TerminalSquare.json
                                        TerminalSquare.tsx
                                        Variable02.json
                                        Variable02.tsx
                                    editor/
                                        Brush01.json
                                        Brush01.tsx
                                        Citations.json
                                        Citations.tsx
                                        Colors.json
                                        Colors.tsx
                                        index.ts
                                        Paragraph.json
                                        Paragraph.tsx
                                        TypeSquare.json
                                        TypeSquare.tsx
                                    education/
                                        Beaker02.json
                                        Beaker02.tsx
                                        BubbleText.json
                                        BubbleText.tsx
                                        Heart02.json
                                        Heart02.tsx
                                        index.ts
                                        Unblur.json
                                        Unblur.tsx
                                    files/
                                        File05.json
                                        File05.tsx
                                        FileSearch02.json
                                        FileSearch02.tsx
                                        FileZip.json
                                        FileZip.tsx
                                        Folder.json
                                        Folder.tsx
                                        index.ts
                                    FinanceAndECommerce/
                                        GoldCoin.json
                                        GoldCoin.tsx
                                        index.ts
                                        Scales02.json
                                        Scales02.tsx
                                    general/
                                        AnswerTriangle.json
                                        AnswerTriangle.tsx
                                        ArrowDownRoundFill.json
                                        ArrowDownRoundFill.tsx
                                        CheckCircle.json
                                        CheckCircle.tsx
                                        CheckDone01.json
                                        CheckDone01.tsx
                                        Download02.json
                                        Download02.tsx
                                        Edit03.json
                                        Edit03.tsx
                                        Edit04.json
                                        Edit04.tsx
                                        Eye.json
                                        Eye.tsx
                                        Github.json
                                        Github.tsx
                                        index.ts
                                        MessageClockCircle.json
                                        MessageClockCircle.tsx
                                        PlusCircle.json
                                        PlusCircle.tsx
                                        QuestionTriangle.json
                                        QuestionTriangle.tsx
                                        SearchMd.json
                                        SearchMd.tsx
                                        Target04.json
                                        Target04.tsx
                                        Tool03.json
                                        Tool03.tsx
                                        XCircle.json
                                        XCircle.tsx
                                        ZapFast.json
                                        ZapFast.tsx
                                        ZapNarrow.json
                                        ZapNarrow.tsx
                                    layout/
                                        Grid01.json
                                        Grid01.tsx
                                        index.ts
                                    mapsAndTravel/
                                        Globe06.json
                                        Globe06.tsx
                                        index.ts
                                        Route.json
                                        Route.tsx
                                    mediaAndDevices/
                                        AudioSupportIcon.json
                                        AudioSupportIcon.tsx
                                        DocumentSupportIcon.json
                                        DocumentSupportIcon.tsx
                                        index.ts
                                        MagicBox.json
                                        MagicBox.tsx
                                        MagicEyes.json
                                        MagicEyes.tsx
                                        MagicWand.json
                                        MagicWand.tsx
                                        Microphone01.json
                                        Microphone01.tsx
                                        Play.json
                                        Play.tsx
                                        Robot.json
                                        Robot.tsx
                                        Sliders02.json
                                        Sliders02.tsx
                                        Speaker.json
                                        Speaker.tsx
                                        StopCircle.json
                                        StopCircle.tsx
                                        VideoSupportIcon.json
                                        VideoSupportIcon.tsx
                                    security/
                                        index.ts
                                        Lock01.json
                                        Lock01.tsx
                                    shapes/
                                        Corner.json
                                        Corner.tsx
                                        index.ts
                                        Star04.json
                                        Star04.tsx
                                        Star06.json
                                        Star06.tsx
                                    users/
                                        index.ts
                                        User01.json
                                        User01.tsx
                                        UserEdit02.json
                                        UserEdit02.tsx
                                        Users01.json
                                        Users01.tsx
                                        UsersPlus.json
                                        UsersPlus.tsx
                                system/
                                    AutoUpdateLine.json
                                    AutoUpdateLine.tsx
                                    index.ts
                                workflow/
                                    Agent.json
                                    Agent.tsx
                                    Answer.json
                                    Answer.tsx
                                    Assigner.json
                                    Assigner.tsx
                                    Code.json
                                    Code.tsx
                                    DocsExtractor.json
                                    DocsExtractor.tsx
                                    End.json
                                    End.tsx
                                    Home.json
                                    Home.tsx
                                    Http.json
                                    Http.tsx
                                    IfElse.json
                                    IfElse.tsx
                                    index.ts
                                    Iteration.json
                                    Iteration.tsx
                                    IterationStart.json
                                    IterationStart.tsx
                                    Jinja.json
                                    Jinja.tsx
                                    KnowledgeRetrieval.json
                                    KnowledgeRetrieval.tsx
                                    ListFilter.json
                                    ListFilter.tsx
                                    Llm.json
                                    Llm.tsx
                                    Loop.json
                                    Loop.tsx
                                    LoopEnd.json
                                    LoopEnd.tsx
                                    ParameterExtractor.json
                                    ParameterExtractor.tsx
                                    QuestionClassifier.json
                                    QuestionClassifier.tsx
                                    TemplatingTransform.json
                                    TemplatingTransform.tsx
                                    VariableX.json
                                    VariableX.tsx
                                    WindowCursor.json
                                    WindowCursor.tsx
                    image-gallery/
                        index.tsx
                        style.module.css
                    image-uploader/
                        audio-preview.tsx
                        chat-image-uploader.tsx
                        hooks.ts
                        image-link-input.tsx
                        image-list.tsx
                        image-preview.tsx
                        text-generation-image-uploader.tsx
                        uploader.tsx
                        utils.ts
                        video-preview.tsx
                    input/
                        index.spec.tsx
                        index.tsx
                    input-number/
                        index.spec.tsx
                        index.tsx
                    install-button/
                        index.tsx
                    linked-apps-panel/
                        index.tsx
                    list-empty/
                        horizontal-line.tsx
                        index.tsx
                        vertical-line.tsx
                    loading/
                        index.spec.tsx
                        index.tsx
                        style.css
                    logo/
                        dify-logo.tsx
                        logo-embedded-chat-avatar.tsx
                        logo-embedded-chat-header.tsx
                        logo-site.tsx
                    markdown/
                        error-boundary.tsx
                        index.tsx
                        markdown-utils.ts
                    markdown-blocks/
                        audio-block.tsx
                        button.tsx
                        code-block.tsx
                        form.tsx
                        img.tsx
                        index.ts
                        link.tsx
                        music.tsx
                        paragraph.tsx
                        pre-code.tsx
                        script-block.tsx
                        think-block.tsx
                        utils.ts
                        video-block.tsx
                    mermaid/
                        index.tsx
                        utils.spec.ts
                        utils.ts
                    message-log-modal/
                        index.tsx
                    modal/
                        index.css
                        index.tsx
                        modal.tsx
                    modal-like-wrap/
                        index.tsx
                    new-audio-button/
                        index.tsx
                    notion-icon/
                        index.tsx
                    notion-page-selector/
                        base.tsx
                        index.tsx
                        assets/
                            clear.svg
                            down-arrow.svg
                            notion-empty-page.svg
                            notion-page.svg
                            search.svg
                            setting.svg
                        notion-page-selector-modal/
                            index.module.css
                            index.tsx
                        page-selector/
                            index.tsx
                        search-input/
                            index.tsx
                        workspace-selector/
                            index.tsx
                    pagination/
                        hook.ts
                        index.tsx
                        pagination.tsx
                        type.ts
                    param-item/
                        index.tsx
                        score-threshold-item.tsx
                        top-k-item.tsx
                    popover/
                        index.tsx
                    portal-to-follow-elem/
                        index.spec.tsx
                        index.tsx
                    premium-badge/
                        index.css
                        index.tsx
                    progress-bar/
                        index.tsx
                        progress-circle.tsx
                    prompt-editor/
                        constants.tsx
                        hooks.ts
                        index.tsx
                        types.ts
                        utils.ts
                        plugins/
                            on-blur-or-focus-block.tsx
                            placeholder.tsx
                            tree-view.tsx
                            update-block.tsx
                            component-picker-block/
                                hooks.tsx
                                index.tsx
                                menu.tsx
                                prompt-option.tsx
                                variable-option.tsx
                            context-block/
                                component.tsx
                                context-block-replacement-block.tsx
                                index.tsx
                                node.tsx
                            custom-text/
                                node.tsx
                            history-block/
                                component.tsx
                                history-block-replacement-block.tsx
                                index.tsx
                                node.tsx
                            query-block/
                                component.tsx
                                index.tsx
                                node.tsx
                                query-block-replacement-block.tsx
                            variable-block/
                                index.tsx
                            variable-value-block/
                                index.tsx
                                node.tsx
                                utils.ts
                            workflow-variable-block/
                                component.tsx
                                index.tsx
                                node.tsx
                                workflow-variable-block-replacement-block.tsx
                    prompt-log-modal/
                        card.tsx
                        index.tsx
                    qrcode/
                        index.tsx
                    radio/
                        index.tsx
                        style.module.css
                        ui.tsx
                        component/
                            group/
                                index.tsx
                            radio/
                                index.tsx
                        context/
                            index.tsx
                    radio-card/
                        index.tsx
                        simple/
                            index.tsx
                            style.module.css
                    search-input/
                        index.tsx
                    segmented-control/
                        index.tsx
                    select/
                        index.tsx
                        locale-signin.tsx
                        locale.tsx
                        pure.tsx
                    simple-pie-chart/
                        index.module.css
                        index.tsx
                    skeleton/
                        index.tsx
                    slider/
                        index.tsx
                        style.css
                    sort/
                        index.tsx
                    spinner/
                        index.spec.tsx
                        index.tsx
                    svg/
                        index.tsx
                        style.module.css
                    svg-gallery/
                        index.tsx
                    switch/
                        index.tsx
                    tab-header/
                        index.tsx
                    tab-slider/
                        index.tsx
                    tab-slider-new/
                        index.tsx
                    tab-slider-plain/
                        index.tsx
                    tag/
                        index.tsx
                    tag-input/
                        index.tsx
                    tag-management/
                        constant.ts
                        filter.tsx
                        index.tsx
                        selector.tsx
                        store.ts
                        tag-item-editor.tsx
                        tag-remove-modal.tsx
                    text-generation/
                        hooks.ts
                        types.ts
                    textarea/
                        index.tsx
                    toast/
                        index.spec.tsx
                        index.tsx
                        style.module.css
                    tooltip/
                        content.tsx
                        index.spec.tsx
                        index.tsx
                    video-gallery/
                        index.tsx
                        VideoPlayer.module.css
                        VideoPlayer.tsx
                    voice-input/
                        index.module.css
                        index.tsx
                        utils.ts
                    with-input-validation/
                        index.spec.tsx
                        index.tsx
                billing/
                    config.ts
                    type.ts
                    annotation-full/
                        index.tsx
                        modal.tsx
                        style.module.css
                        usage.tsx
                    apps-full-in-dialog/
                        index.tsx
                        style.module.css
                    billing-page/
                        index.tsx
                    header-billing-btn/
                        index.tsx
                    plan/
                        index.tsx
                    pricing/
                        index.tsx
                        plan-item.tsx
                        select-plan-range.tsx
                        self-hosted-plan-item.tsx
                    priority-label/
                        index.tsx
                    progress-bar/
                        index.tsx
                    upgrade-btn/
                        index.tsx
                        style.module.css
                    usage-info/
                        apps-info.tsx
                        index.tsx
                        vector-space-info.tsx
                    utils/
                        index.ts
                    vector-space-full/
                        index.tsx
                        style.module.css
                custom/
                    style.module.css
                    custom-page/
                        index.tsx
                    custom-web-app-brand/
                        index.tsx
                        style.module.css
                datasets/
                    chunk.tsx
                    loading.tsx
                    api/
                        index.tsx
                    common/
                        check-rerank-model.ts
                        chunking-mode-label.tsx
                        document-file-icon.tsx
                        document-picker/
                            document-list.tsx
                            index.tsx
                            preview-document-picker.tsx
                        document-status-with-action/
                            auto-disabled-document.tsx
                            index-failed.tsx
                            status-with-action.tsx
                        economical-retrieval-method-config/
                            index.tsx
                        retrieval-method-config/
                            index.tsx
                        retrieval-method-info/
                            index.tsx
                        retrieval-param-config/
                            index.tsx
                    create/
                        icons.ts
                        index.module.css
                        index.tsx
                        assets/
                            alert-triangle.svg
                            annotation-info.svg
                            arrow-narrow-left.svg
                            book-open-01.svg
                            check.svg
                            close.svg
                            csv.svg
                            doc.svg
                            docx.svg
                            family-mod.svg
                            file-list-3-fill.svg
                            file.svg
                            folder-plus.svg
                            gold.svg
                            html.svg
                            Icon-3-dots.svg
                            jina.png
                            json.svg
                            Loading.svg
                            md.svg
                            normal.svg
                            note-mod.svg
                            notion.svg
                            option-card-effect-blue.svg
                            option-card-effect-orange.svg
                            option-card-effect-purple.svg
                            pattern-recognition-mod.svg
                            pdf.svg
                            piggy-bank-01.svg
                            piggy-bank-mod.svg
                            progress-indicator.svg
                            rerank.svg
                            research-mod.svg
                            selection-mod.svg
                            setting-gear-mod.svg
                            sliders-02.svg
                            star-07.svg
                            star.svg
                            trash.svg
                            txt.svg
                            unknown.svg
                            upload-cloud-01.svg
                            watercrawl.svg
                            web.svg
                            xlsx.svg
                            zap-fast.svg
                        embedding-process/
                            index.module.css
                            index.tsx
                        empty-dataset-creation-modal/
                            index.module.css
                            index.tsx
                        file-preview/
                            index.module.css
                            index.tsx
                        file-uploader/
                            index.module.css
                            index.tsx
                        notion-page-preview/
                            index.module.css
                            index.tsx
                        step-one/
                            index.module.css
                            index.tsx
                        step-three/
                            index.module.css
                            index.tsx
                        step-two/
                            escape.ts
                            index.module.css
                            index.tsx
                            inputs.tsx
                            option-card.tsx
                            unescape.ts
                            language-select/
                                index.tsx
                            preview-item/
                                index.tsx
                        stepper/
                            index.tsx
                            step.tsx
                        stop-embedding-modal/
                            index.module.css
                            index.tsx
                        top-bar/
                            index.tsx
                        website/
                            index.module.css
                            index.tsx
                            no-data.tsx
                            preview.tsx
                            base/
                                checkbox-with-label.tsx
                                crawled-result-item.tsx
                                crawled-result.tsx
                                crawling.tsx
                                error-message.tsx
                                field.tsx
                                input.tsx
                                mock-crawl-result.ts
                                options-wrap.tsx
                                url-input.tsx
                            firecrawl/
                                header.tsx
                                index.tsx
                                options.tsx
                            jina-reader/
                                header.tsx
                                index.tsx
                                options.tsx
                                base/
                                    checkbox-with-label.tsx
                                    error-message.tsx
                                    field.tsx
                                    input.tsx
                                    options-wrap.tsx
                                    url-input.tsx
                            watercrawl/
                                header.tsx
                                index.tsx
                                options.tsx
                    documents/
                        index.tsx
                        list.tsx
                        rename-modal.tsx
                        style.module.css
                        assets/
                            atSign.svg
                            bezierCurve.svg
                            bookOpen.svg
                            briefcase.svg
                            cardLoading.svg
                            file.svg
                            globe.svg
                            graduationHat.svg
                            hitLoading.svg
                            layoutRightClose.svg
                            layoutRightShow.svg
                            messageTextCircle.svg
                            normal.svg
                            star.svg
                            target.svg
                            typeSquare.svg
                        detail/
                            index.tsx
                            new-segment.tsx
                            style.module.css
                            batch-modal/
                                csv-downloader.tsx
                                csv-uploader.tsx
                                index.tsx
                            completed/
                                child-segment-detail.tsx
                                child-segment-list.tsx
                                display-toggle.tsx
                                index.tsx
                                new-child-segment.tsx
                                segment-detail.tsx
                                segment-list.tsx
                                status-item.tsx
                                style.module.css
                                common/
                                    action-buttons.tsx
                                    add-another.tsx
                                    batch-action.tsx
                                    chunk-content.tsx
                                    dot.tsx
                                    empty.tsx
                                    full-screen-drawer.tsx
                                    keywords.tsx
                                    regeneration-modal.tsx
                                    segment-index-tag.tsx
                                    tag.tsx
                                segment-card/
                                    chunk-content.tsx
                                    index.tsx
                                skeleton/
                                    full-doc-list-skeleton.tsx
                                    general-list-skeleton.tsx
                                    paragraph-list-skeleton.tsx
                                    parent-chunk-card-skeleton.tsx
                            embedding/
                                index.tsx
                                style.module.css
                                skeleton/
                                    index.tsx
                            metadata/
                                index.tsx
                                style.module.css
                            segment-add/
                                index.tsx
                            settings/
                                index.tsx
                        hooks/
                            use-document-list-query-state.ts
                    external-api/
                        declarations.ts
                        external-api-modal/
                            Form.tsx
                            index.tsx
                        external-api-panel/
                            index.tsx
                        external-knowledge-api-card/
                            index.tsx
                    external-knowledge-base/
                        connector/
                            index.tsx
                        create/
                            declarations.ts
                            ExternalApiSelect.tsx
                            ExternalApiSelection.tsx
                            index.tsx
                            InfoPanel.tsx
                            KnowledgeBaseInfo.tsx
                            RetrievalSettings.tsx
                    formatted-text/
                        formatted.tsx
                        flavours/
                            edit-slice.tsx
                            preview-slice.tsx
                            shared.tsx
                            type.ts
                    hit-testing/
                        index.tsx
                        modify-external-retrieval-modal.tsx
                        modify-retrieval-modal.tsx
                        style.module.css
                        textarea.tsx
                        assets/
                            clock.svg
                            grid.svg
                            plugin.svg
                        components/
                            child-chunks-item.tsx
                            chunk-detail-modal.tsx
                            result-item-external.tsx
                            result-item-footer.tsx
                            result-item-meta.tsx
                            result-item.tsx
                            score.tsx
                        utils/
                            extension-to-file-type.ts
                    metadata/
                        add-metadata-button.tsx
                        types.ts
                        base/
                            date-picker.tsx
                        edit-metadata-batch/
                            add-row.tsx
                            edit-row.tsx
                            edited-beacon.tsx
                            input-combined.tsx
                            input-has-set-multiple-value.tsx
                            label.tsx
                            modal.tsx
                        hooks/
                            use-batch-edit-document-metadata.ts
                            use-check-metadata-name.ts
                            use-edit-dataset-metadata.ts
                            use-metadata-document.ts
                        metadata-dataset/
                            create-content.tsx
                            create-metadata-modal.tsx
                            dataset-metadata-drawer.tsx
                            field.tsx
                            select-metadata-modal.tsx
                            select-metadata.tsx
                        metadata-document/
                            field.tsx
                            index.tsx
                            info-group.tsx
                            no-data.tsx
                        utils/
                            get-icon.ts
                    preview/
                        container.tsx
                        header.tsx
                        index.tsx
                    rename-modal/
                        index.tsx
                    settings/
                        form/
                            index.tsx
                        index-method-radio/
                            index.tsx
                            assets/
                                economy.svg
                                high-quality.svg
                        permission-selector/
                            index.tsx
                develop/
                    ApiServer.tsx
                    code.tsx
                    doc.tsx
                    index.tsx
                    md.tsx
                    tag.tsx
                    secret-key/
                        input-copy.tsx
                        secret-key-button.tsx
                        secret-key-generate.tsx
                        secret-key-modal.tsx
                        style.module.css
                        assets/
                            copied.svg
                            copy-hover.svg
                            copy.svg
                            pause.svg
                            play.svg
                            qrcode-hover.svg
                            qrcode.svg
                            svg.svg
                            svged.svg
                            trash-gray.svg
                            trash-red.svg
                    template/
                        template.en.mdx
                        template.ja.mdx
                        template.zh.mdx
                        template_advanced_chat.en.mdx
                        template_advanced_chat.ja.mdx
                        template_advanced_chat.zh.mdx
                        template_chat.en.mdx
                        template_chat.ja.mdx
                        template_chat.zh.mdx
                        template_workflow.en.mdx
                        template_workflow.ja.mdx
                        template_workflow.zh.mdx
                explore/
                    category.tsx
                    index.tsx
                    app-card/
                        index.tsx
                    app-list/
                        index.tsx
                        style.module.css
                    create-app-modal/
                        index.tsx
                    installed-app/
                        index.tsx
                    item-operation/
                        index.tsx
                        style.module.css
                    sidebar/
                        index.tsx
                        app-nav-item/
                            index.tsx
                goto-anything/
                    command-selector.tsx
                    context.tsx
                    index.tsx
                    actions/
                        app.tsx
                        index.ts
                        knowledge.tsx
                        plugin.tsx
                        types.ts
                        workflow-nodes.tsx
                header/
                    header-wrapper.tsx
                    index.module.css
                    index.tsx
                    maintenance-notice.tsx
                    account-about/
                        index.tsx
                    account-dropdown/
                        compliance.tsx
                        index.tsx
                        support.tsx
                        workplace-selector/
                            index.module.css
                            index.tsx
                    account-setting/
                        index.tsx
                        menu-dialog.tsx
                        api-based-extension-page/
                            empty.tsx
                            index.tsx
                            item.tsx
                            modal.tsx
                            selector.tsx
                        collapse/
                            index.tsx
                        data-source-page/
                            index.module.css
                            index.tsx
                            data-source-notion/
                                index.tsx
                                operate/
                                    index.tsx
                            data-source-website/
                                config-firecrawl-modal.tsx
                                config-jina-reader-modal.tsx
                                config-watercrawl-modal.tsx
                                index.tsx
                            panel/
                                config-item.tsx
                                index.tsx
                                style.module.css
                                types.ts
                        Integrations-page/
                            index.module.css
                            index.tsx
                        key-validator/
                            declarations.ts
                            hooks.ts
                            index.tsx
                            KeyInput.tsx
                            Operate.tsx
                            ValidateStatus.tsx
                        language-page/
                            index.module.css
                            index.tsx
                        members-page/
                            index.tsx
                            edit-workspace-modal/
                                index.module.css
                                index.tsx
                            invite-modal/
                                index.module.css
                                index.tsx
                                role-selector.tsx
                            invited-modal/
                                index.module.css
                                index.tsx
                                invitation-link.tsx
                                assets/
                                    copied.svg
                                    copy-hover.svg
                                    copy.svg
                            operation/
                                index.tsx
                                transfer-ownership.tsx
                            transfer-ownership-modal/
                                index.tsx
                                member-selector.tsx
                        model-provider-page/
                            declarations.ts
                            hooks.spec.ts
                            hooks.ts
                            index.tsx
                            install-from-marketplace.tsx
                            utils.ts
                            model-badge/
                                index.tsx
                            model-icon/
                                index.tsx
                            model-modal/
                                Form.tsx
                                index.tsx
                                Input.tsx
                                model-load-balancing-entry-modal.tsx
                            model-name/
                                index.tsx
                            model-parameter-modal/
                                agent-model-trigger.tsx
                                configuration-button.tsx
                                index.tsx
                                model-display.tsx
                                parameter-item.tsx
                                presets-parameter.tsx
                                status-indicators.tsx
                                trigger.tsx
                            model-selector/
                                deprecated-model-trigger.tsx
                                empty-trigger.tsx
                                feature-icon.tsx
                                index.tsx
                                model-trigger.tsx
                                popup-item.tsx
                                popup.tsx
                            provider-added-card/
                                add-model-button.tsx
                                cooldown-timer.tsx
                                credential-panel.tsx
                                index.tsx
                                model-list-item.tsx
                                model-list.tsx
                                model-load-balancing-configs.tsx
                                model-load-balancing-modal.tsx
                                priority-selector.tsx
                                priority-use-tip.tsx
                                quota-panel.tsx
                            provider-icon/
                                index.tsx
                            system-model-selector/
                                index.tsx
                        plugin-page/
                            index.tsx
                            SerpapiPlugin.tsx
                            utils.ts
                    app-back/
                        index.tsx
                    app-nav/
                        index.tsx
                    app-selector/
                        index.tsx
                    assets/
                        alpha.svg
                        anthropic.svg
                        azure.svg
                        bitbucket.svg
                        file.svg
                        github.svg
                        google.svg
                        gpt.svg
                        hugging-face.svg
                        notion.svg
                        salesforce.svg
                        serpapi.png
                        sync.svg
                        trash.svg
                        twitter.svg
                    dataset-nav/
                        index.tsx
                    env-nav/
                        index.tsx
                    explore-nav/
                        index.tsx
                    github-star/
                        index.tsx
                    indicator/
                        index.tsx
                    license-env/
                        index.tsx
                    nav/
                        index.module.css
                        index.tsx
                        nav-selector/
                            index.tsx
                    plan-badge/
                        index.tsx
                    plugins-nav/
                        downloading-icon.module.css
                        downloading-icon.tsx
                        index.tsx
                    tools-nav/
                        index.tsx
                    utils/
                        util.ts
                plugins/
                    constants.ts
                    hooks.ts
                    provider-card.tsx
                    types.ts
                    utils.ts
                    base/
                        deprecation-notice.tsx
                        key-value-item.tsx
                        badges/
                            icon-with-tooltip.tsx
                            partner.tsx
                            verified.tsx
                    card/
                        card-more-info.tsx
                        index.tsx
                        base/
                            card-icon.tsx
                            corner-mark.tsx
                            description.tsx
                            download-count.tsx
                            org-info.tsx
                            placeholder.tsx
                            title.tsx
                    install-plugin/
                        hooks.ts
                        utils.ts
                        base/
                            check-task-status.ts
                            installed.tsx
                            loading-error.tsx
                            loading.tsx
                            use-get-icon.ts
                            version.tsx
                        hooks/
                            use-check-installed.tsx
                            use-fold-anim-into.ts
                            use-hide-logic.ts
                            use-install-plugin-limit.tsx
                            use-refresh-plugin-list.tsx
                        install-bundle/
                            index.tsx
                            ready-to-install.tsx
                            item/
                                github-item.tsx
                                loaded-item.tsx
                                marketplace-item.tsx
                                package-item.tsx
                            steps/
                                install-multi.tsx
                                install.tsx
                                installed.tsx
                        install-from-github/
                            index.tsx
                            steps/
                                loaded.tsx
                                selectPackage.tsx
                                setURL.tsx
                        install-from-local-package/
                            index.tsx
                            ready-to-install.tsx
                            steps/
                                install.tsx
                                uploading.tsx
                        install-from-marketplace/
                            index.tsx
                            steps/
                                install.tsx
                    marketplace/
                        constants.ts
                        context.tsx
                        hooks.ts
                        index.tsx
                        plugin-type-switch.tsx
                        types.ts
                        utils.ts
                        description/
                            index.tsx
                        empty/
                            index.tsx
                            line.tsx
                        intersection-line/
                            hooks.ts
                            index.tsx
                        list/
                            card-wrapper.tsx
                            index.tsx
                            list-with-collection.tsx
                            list-wrapper.tsx
                        search-box/
                            index.tsx
                            search-box-wrapper.tsx
                            tags-filter.tsx
                        sort-dropdown/
                            index.tsx
                    plugin-auth/
                        authorized-in-node.tsx
                        index.tsx
                        plugin-auth-in-agent.tsx
                        plugin-auth.tsx
                        types.ts
                        utils.ts
                        authorize/
                            add-api-key-button.tsx
                            add-oauth-button.tsx
                            api-key-modal.tsx
                            index.tsx
                            oauth-client-settings.tsx
                        authorized/
                            index.tsx
                            item.tsx
                        hooks/
                            use-credential.ts
                            use-get-api.ts
                            use-plugin-auth.ts
                    plugin-detail-panel/
                        action-list.tsx
                        agent-strategy-list.tsx
                        detail-header.tsx
                        endpoint-card.tsx
                        endpoint-list.tsx
                        endpoint-modal.tsx
                        index.tsx
                        model-list.tsx
                        operation-dropdown.tsx
                        strategy-detail.tsx
                        strategy-item.tsx
                        utils.ts
                        app-selector/
                            app-inputs-form.tsx
                            app-inputs-panel.tsx
                            app-picker.tsx
                            app-trigger.tsx
                            index.tsx
                        model-selector/
                            index.tsx
                            llm-params-panel.tsx
                            tts-params-panel.tsx
                        multiple-tool-selector/
                            index.tsx
                        tool-selector/
                            hooks.ts
                            index.tsx
                            reasoning-config-form.tsx
                            schema-modal.tsx
                            tool-credentials-form.tsx
                            tool-item.tsx
                            tool-trigger.tsx
                    plugin-item/
                        action.tsx
                        index.tsx
                    plugin-mutation-model/
                        index.tsx
                    plugin-page/
                        context.tsx
                        debug-info.tsx
                        index.tsx
                        install-plugin-dropdown.tsx
                        plugin-info.tsx
                        plugins-panel.tsx
                        use-reference-setting.ts
                        use-uploader.ts
                        empty/
                            index.tsx
                        filter-management/
                            category-filter.tsx
                            constant.ts
                            index.tsx
                            search-box.tsx
                            store.ts
                            tag-filter.tsx
                        list/
                            index.tsx
                        plugin-tasks/
                            hooks.ts
                            index.tsx
                    reference-setting-modal/
                        label.tsx
                        modal.tsx
                        style.module.css
                        auto-update-setting/
                            config.ts
                            index.tsx
                            no-data-placeholder.tsx
                            no-plugin-selected.tsx
                            plugins-picker.tsx
                            plugins-selected.tsx
                            strategy-picker.tsx
                            tool-item.tsx
                            tool-picker.tsx
                            types.ts
                            utils.spec.ts
                            utils.ts
                    update-plugin/
                        downgrade-warning.tsx
                        from-github.tsx
                        from-market-place.tsx
                        index.tsx
                        plugin-version-picker.tsx
                share/
                    utils.ts
                    text-generation/
                        index.tsx
                        info-modal.tsx
                        menu-dropdown.tsx
                        icons/
                            star.svg
                        no-data/
                            index.tsx
                        result/
                            content.tsx
                            header.tsx
                            index.tsx
                        run-batch/
                            index.tsx
                            csv-download/
                                index.tsx
                            csv-reader/
                                index.tsx
                            res-download/
                                index.tsx
                        run-once/
                            index.tsx
                signin/
                    countdown.tsx
                tools/
                    provider-list.tsx
                    types.ts
                    add-tool-modal/
                        category.tsx
                        D.png
                        empty.png
                        empty.tsx
                        index.tsx
                        tools.tsx
                        type.tsx
                    edit-custom-collection-modal/
                        config-credentials.tsx
                        examples.ts
                        get-schema.tsx
                        index.tsx
                        modal.tsx
                        test-api.tsx
                    labels/
                        constant.ts
                        filter.tsx
                        selector.tsx
                    marketplace/
                        hooks.ts
                        index.tsx
                    mcp/
                        create-card.tsx
                        index.tsx
                        mcp-server-modal.tsx
                        mcp-server-param-item.tsx
                        mcp-service-card.tsx
                        mock.ts
                        modal.tsx
                        provider-card.tsx
                        detail/
                            content.tsx
                            list-loading.tsx
                            operation-dropdown.tsx
                            provider-detail.tsx
                            tool-item.tsx
                    provider/
                        custom-create-card.tsx
                        detail.tsx
                        tool-item.tsx
                    setting/
                        build-in/
                            config-credentials.tsx
                    utils/
                        index.ts
                        to-form-schema.ts
                    workflow-tool/
                        configure-button.tsx
                        index.tsx
                        method-selector.tsx
                        confirm-modal/
                            index.tsx
                workflow/
                    block-icon.tsx
                    blocks.tsx
                    candidate-node.tsx
                    constants.ts
                    context.tsx
                    custom-connection-line.tsx
                    custom-edge-linear-gradient-render.tsx
                    custom-edge.tsx
                    dsl-export-confirm-modal.tsx
                    features.tsx
                    index.tsx
                    limit-tips.tsx
                    node-contextmenu.tsx
                    panel-contextmenu.tsx
                    selection-contextmenu.tsx
                    shortcuts-name.tsx
                    style.css
                    syncing-data-modal.tsx
                    types.ts
                    update-dsl-modal.tsx
                    workflow-history-store.tsx
                    block-selector/
                        all-tools.tsx
                        blocks.tsx
                        constants.tsx
                        hooks.ts
                        index-bar.tsx
                        index.tsx
                        tabs.tsx
                        tool-picker.tsx
                        tools.tsx
                        types.ts
                        use-check-vertical-scrollbar.ts
                        use-sticky-scroll.ts
                        view-type-select.tsx
                        market-place-plugin/
                            action.tsx
                            item.tsx
                            list.tsx
                        tool/
                            action-item.tsx
                            tool.tsx
                            tool-list-flat-view/
                                list.tsx
                            tool-list-tree-view/
                                item.tsx
                                list.tsx
                    datasets-detail-store/
                        provider.tsx
                        store.ts
                    header/
                        chat-variable-button.tsx
                        checklist.tsx
                        editing-title.tsx
                        env-button.tsx
                        global-variable-button.tsx
                        header-in-normal.tsx
                        header-in-restoring.tsx
                        header-in-view-history.tsx
                        index.tsx
                        restoring-title.tsx
                        run-and-history.tsx
                        running-title.tsx
                        undo-redo.tsx
                        version-history-button.tsx
                        view-history.tsx
                        view-workflow-history.tsx
                    help-line/
                        index.tsx
                        types.ts
                    hooks/
                        index.ts
                        use-checklist.ts
                        use-config-vision.ts
                        use-edges-interactions-without-sync.ts
                        use-edges-interactions.ts
                        use-fetch-workflow-inspect-vars.ts
                        use-helpline.ts
                        use-inspect-vars-crud-common.ts
                        use-inspect-vars-crud.ts
                        use-node-data-update.ts
                        use-nodes-available-var-list.ts
                        use-nodes-data.ts
                        use-nodes-interactions-without-sync.ts
                        use-nodes-interactions.ts
                        use-nodes-layout.ts
                        use-nodes-sync-draft.ts
                        use-panel-interactions.ts
                        use-selection-interactions.ts
                        use-set-workflow-vars-with-value.ts
                        use-shortcuts.ts
                        use-workflow-history.ts
                        use-workflow-interactions.ts
                        use-workflow-mode.ts
                        use-workflow-refresh-draft.ts
                        use-workflow-run.ts
                        use-workflow-search.tsx
                        use-workflow-start-run.tsx
                        use-workflow-variables.ts
                        use-workflow.ts
                        use-workflow-run-event/
                            index.ts
                            use-workflow-agent-log.ts
                            use-workflow-failed.ts
                            use-workflow-finished.ts
                            use-workflow-node-finished.ts
                            use-workflow-node-iteration-finished.ts
                            use-workflow-node-iteration-next.ts
                            use-workflow-node-iteration-started.ts
                            use-workflow-node-loop-finished.ts
                            use-workflow-node-loop-next.ts
                            use-workflow-node-loop-started.ts
                            use-workflow-node-retry.ts
                            use-workflow-node-started.ts
                            use-workflow-run-event.ts
                            use-workflow-started.ts
                            use-workflow-text-chunk.ts
                            use-workflow-text-replace.ts
                    hooks-store/
                        index.ts
                        provider.tsx
                        store.ts
                    nodes/
                        constants.ts
                        index.tsx
                        utils.ts
                        agent/
                            default.ts
                            node.tsx
                            panel.tsx
                            types.ts
                            use-config.ts
                            use-single-run-form-params.ts
                            components/
                                model-bar.tsx
                                tool-icon.tsx
                        answer/
                            default.ts
                            node.tsx
                            panel.tsx
                            types.ts
                            use-config.ts
                            utils.ts
                        assigner/
                            default.ts
                            hooks.ts
                            node.tsx
                            panel.tsx
                            types.ts
                            use-config.ts
                            use-single-run-form-params.ts
                            utils.ts
                            components/
                                operation-selector.tsx
                                var-list/
                                    index.tsx
                                    use-var-list.ts
                        code/
                            code-parser.spec.ts
                            code-parser.ts
                            default.ts
                            dependency-picker.tsx
                            node.tsx
                            panel.tsx
                            types.ts
                            use-config.ts
                            use-single-run-form-params.ts
                            utils.ts
                        document-extractor/
                            default.ts
                            node.tsx
                            panel.tsx
                            types.ts
                            use-config.ts
                            use-single-run-form-params.ts
                        end/
                            default.ts
                            node.tsx
                            panel.tsx
                            types.ts
                            use-config.ts
                            utils.ts
                        http/
                            default.ts
                            node.tsx
                            panel.tsx
                            types.ts
                            use-config.ts
                            use-single-run-form-params.ts
                            utils.ts
                            components/
                                api-input.tsx
                                curl-panel.tsx
                                authorization/
                                    index.tsx
                                    radio-group.tsx
                                edit-body/
                                    index.tsx
                                key-value/
                                    index.tsx
                                    bulk-edit/
                                        index.tsx
                                    key-value-edit/
                                        index.tsx
                                        input-item.tsx
                                        item.tsx
                                timeout/
                                    index.tsx
                            hooks/
                                use-key-value-list.ts
                        if-else/
                            default.ts
                            node.tsx
                            panel.tsx
                            types.ts
                            use-config.ts
                            use-is-var-file-attribute.ts
                            use-single-run-form-params.ts
                            utils.ts
                            components/
                                condition-add.tsx
                                condition-files-list-value.tsx
                                condition-number-input.tsx
                                condition-value.tsx
                                condition-wrap.tsx
                                condition-list/
                                    condition-input.tsx
                                    condition-item.tsx
                                    condition-operator.tsx
                                    condition-var-selector.tsx
                                    index.tsx
                        iteration/
                            add-block.tsx
                            default.ts
                            node.tsx
                            panel.tsx
                            types.ts
                            use-config.ts
                            use-interactions.ts
                            use-single-run-form-params.ts
                        iteration-start/
                            constants.ts
                            default.ts
                            index.tsx
                            types.ts
                        knowledge-retrieval/
                            default.ts
                            hooks.ts
                            node.tsx
                            panel.tsx
                            types.ts
                            use-config.ts
                            use-single-run-form-params.ts
                            utils.ts
                            components/
                                add-dataset.tsx
                                dataset-item.tsx
                                dataset-list.tsx
                                retrieval-config.tsx
                                metadata/
                                    add-condition.tsx
                                    metadata-icon.tsx
                                    metadata-panel.tsx
                                    metadata-trigger.tsx
                                    condition-list/
                                        condition-common-variable-selector.tsx
                                        condition-date.tsx
                                        condition-item.tsx
                                        condition-number.tsx
                                        condition-operator.tsx
                                        condition-string.tsx
                                        condition-value-method.tsx
                                        condition-variable-selector.tsx
                                        index.tsx
                                        utils.ts
                                    metadata-filter/
                                        index.tsx
                                        metadata-filter-selector.tsx
                        list-operator/
                            default.ts
                            node.tsx
                            panel.tsx
                            types.ts
                            use-config.ts
                            components/
                                extract-input.tsx
                                filter-condition.tsx
                                limit-config.tsx
                                sub-variable-picker.tsx
                        llm/
                            default.ts
                            node.tsx
                            panel.tsx
                            types.ts
                            use-config.ts
                            use-single-run-form-params.ts
                            utils.ts
                            components/
                                config-prompt-item.tsx
                                config-prompt.tsx
                                prompt-generator-btn.tsx
                                resolution-picker.tsx
                                structure-output.tsx
                                json-schema-config-modal/
                                    code-editor.tsx
                                    error-message.tsx
                                    index.tsx
                                    json-importer.tsx
                                    json-schema-config.tsx
                                    schema-editor.tsx
                                    json-schema-generator/
                                        generated-result.tsx
                                        index.tsx
                                        prompt-editor.tsx
                                        assets/
                                            index.tsx
                                            schema-generator-dark.tsx
                                            schema-generator-light.tsx
                                    visual-editor/
                                        add-field.tsx
                                        card.tsx
                                        context.tsx
                                        hooks.ts
                                        index.tsx
                                        schema-node.tsx
                                        store.ts
                                        edit-card/
                                            actions.tsx
                                            advanced-actions.tsx
                                            advanced-options.tsx
                                            auto-width-input.tsx
                                            index.tsx
                                            required-switch.tsx
                                            type-selector.tsx
                        loop/
                            add-block.tsx
                            default.ts
                            insert-block.tsx
                            node.tsx
                            panel.tsx
                            types.ts
                            use-config.ts
                            use-interactions.ts
                            use-is-var-file-attribute.ts
                            use-single-run-form-params.ts
                            utils.ts
                            components/
                                condition-add.tsx
                                condition-files-list-value.tsx
                                condition-number-input.tsx
                                condition-value.tsx
                                condition-wrap.tsx
                                condition-list/
                                    condition-input.tsx
                                    condition-item.tsx
                                    condition-operator.tsx
                                    condition-var-selector.tsx
                                    index.tsx
                                loop-variables/
                                    empty.tsx
                                    form-item.tsx
                                    index.tsx
                                    input-mode-selec.tsx
                                    item.tsx
                                    variable-type-select.tsx
                        loop-end/
                            default.ts
                        loop-start/
                            constants.ts
                            default.ts
                            index.tsx
                            types.ts
                        parameter-extractor/
                            default.ts
                            node.tsx
                            panel.tsx
                            types.ts
                            use-config.ts
                            use-single-run-form-params.ts
                            components/
                                reasoning-mode-picker.tsx
                                extract-parameter/
                                    import-from-tool.tsx
                                    item.tsx
                                    list.tsx
                                    update.tsx
                        question-classifier/
                            default.ts
                            node.tsx
                            panel.tsx
                            types.ts
                            use-config.ts
                            use-single-run-form-params.ts
                            utils.ts
                            components/
                                advanced-setting.tsx
                                class-item.tsx
                                class-list.tsx
                        start/
                            default.ts
                            node.tsx
                            panel.tsx
                            types.ts
                            use-config.ts
                            use-single-run-form-params.ts
                            utils.ts
                            components/
                                var-item.tsx
                                var-list.tsx
                        template-transform/
                            default.ts
                            node.tsx
                            panel.tsx
                            types.ts
                            use-config.ts
                            use-single-run-form-params.ts
                            utils.ts
                        tool/
                            default.ts
                            node.tsx
                            panel.tsx
                            types.ts
                            use-config.ts
                            use-get-data-for-check-more.ts
                            use-single-run-form-params.ts
                            utils.ts
                            components/
                                copy-id.tsx
                                input-var-list.tsx
                                mixed-variable-text-input/
                                    index.tsx
                                    placeholder.tsx
                                tool-form/
                                    index.tsx
                                    item.tsx
                        variable-assigner/
                            default.ts
                            hooks.ts
                            node.tsx
                            panel.tsx
                            types.ts
                            use-config.ts
                            use-single-run-form-params.ts
                            utils.ts
                            components/
                                node-group-item.tsx
                                var-group-item.tsx
                                add-variable/
                                    index.tsx
                                var-list/
                                    index.tsx
                                    use-var-list.ts
                        _base/
                            node.tsx
                            components/
                                add-button.tsx
                                add-variable-popup-with-position.tsx
                                add-variable-popup.tsx
                                agent-strategy-selector.tsx
                                agent-strategy.tsx
                                code-generator-button.tsx
                                config-vision.tsx
                                field.tsx
                                file-type-item.tsx
                                file-upload-setting.tsx
                                form-input-boolean.tsx
                                form-input-item.tsx
                                form-input-type-switch.tsx
                                group.tsx
                                help-link.tsx
                                info-panel.tsx
                                input-number-with-slider.tsx
                                input-support-select-var.tsx
                                input-var-type-icon.tsx
                                install-plugin-button.tsx
                                list-no-data-placeholder.tsx
                                mcp-tool-not-support-tooltip.tsx
                                memory-config.tsx
                                node-control.tsx
                                node-handle.tsx
                                node-position.tsx
                                node-resizer.tsx
                                node-status-icon.tsx
                                option-card.tsx
                                output-vars.tsx
                                readonly-input-with-select-var.tsx
                                remove-button.tsx
                                remove-effect-var-confirm.tsx
                                selector.tsx
                                setting-item.tsx
                                split.tsx
                                switch-plugin-version.tsx
                                title-description-input.tsx
                                toggle-expand-btn.tsx
                                variable-tag.tsx
                                before-run-form/
                                    form-item.tsx
                                    form.tsx
                                    index.tsx
                                    panel-wrap.tsx
                                collapse/
                                    field-collapse.tsx
                                    index.tsx
                                editor/
                                    base.tsx
                                    text-editor.tsx
                                    wrap.tsx
                                    code-editor/
                                        editor-support-vars.tsx
                                        index.tsx
                                        style.css
                                error-handle/
                                    default-value.tsx
                                    error-handle-on-node.tsx
                                    error-handle-on-panel.tsx
                                    error-handle-tip.tsx
                                    error-handle-type-selector.tsx
                                    fail-branch-card.tsx
                                    hooks.ts
                                    types.ts
                                    utils.ts
                                next-step/
                                    add.tsx
                                    container.tsx
                                    index.tsx
                                    item.tsx
                                    line.tsx
                                    operator.tsx
                                panel-operator/
                                    change-block.tsx
                                    index.tsx
                                    panel-operator-popup.tsx
                                prompt/
                                    editor.tsx
                                retry/
                                    hooks.ts
                                    retry-on-node.tsx
                                    retry-on-panel.tsx
                                    style.module.css
                                    types.ts
                                    utils.ts
                                support-var-input/
                                    index.tsx
                                variable/
                                    assigned-var-reference-popup.tsx
                                    constant-field.tsx
                                    output-var-list.tsx
                                    utils.ts
                                    var-full-path-panel.tsx
                                    var-list.tsx
                                    var-reference-picker.tsx
                                    var-reference-popup.tsx
                                    var-reference-vars.tsx
                                    var-type-picker.tsx
                                    object-child-tree-panel/
                                        tree-indent-line.tsx
                                        picker/
                                            field.tsx
                                            index.tsx
                                        show/
                                            field.tsx
                                            index.tsx
                                    variable-label/
                                        hooks.ts
                                        index.tsx
                                        types.ts
                                        variable-icon-with-color.tsx
                                        variable-label-in-editor.tsx
                                        variable-label-in-node.tsx
                                        variable-label-in-select.tsx
                                        variable-label-in-text.tsx
                                        base/
                                            variable-icon.tsx
                                            variable-label.tsx
                                            variable-name.tsx
                                            variable-node-label.tsx
                                workflow-panel/
                                    index.spec.tsx
                                    index.tsx
                                    tab.tsx
                                    last-run/
                                        index.tsx
                                        no-data.tsx
                                        use-last-run.ts
                            hooks/
                                use-available-var-list.ts
                                use-node-crud.ts
                                use-node-help-link.ts
                                use-node-info.ts
                                use-one-step-run.ts
                                use-output-var-list.ts
                                use-resize-panel.ts
                                use-toggle-expend.ts
                                use-var-list.ts
                    note-node/
                        constants.ts
                        hooks.ts
                        index.tsx
                        types.ts
                        note-editor/
                            context.tsx
                            editor.tsx
                            index.tsx
                            store.ts
                            utils.ts
                            plugins/
                                format-detector-plugin/
                                    hooks.ts
                                    index.tsx
                                link-editor-plugin/
                                    component.tsx
                                    hooks.ts
                                    index.tsx
                            theme/
                                index.ts
                                theme.css
                            toolbar/
                                color-picker.tsx
                                command.tsx
                                divider.tsx
                                font-size-selector.tsx
                                hooks.ts
                                index.tsx
                                operator.tsx
                    operator/
                        add-block.tsx
                        control.tsx
                        export-image.tsx
                        hooks.ts
                        index.tsx
                        tip-popup.tsx
                        zoom-in-out.tsx
                    panel/
                        index.tsx
                        inputs-panel.tsx
                        record.tsx
                        workflow-preview.tsx
                        chat-record/
                            index.tsx
                            user-input.tsx
                        chat-variable-panel/
                            index.tsx
                            type.ts
                            components/
                                array-value-list.tsx
                                object-value-item.tsx
                                object-value-list.tsx
                                variable-item.tsx
                                variable-modal-trigger.tsx
                                variable-modal.tsx
                                variable-type-select.tsx
                        debug-and-preview/
                            chat-wrapper.tsx
                            conversation-variable-modal.tsx
                            empty.tsx
                            hooks.ts
                            index.spec.tsx
                            index.tsx
                            user-input.tsx
                        env-panel/
                            env-item.tsx
                            index.tsx
                            variable-modal.tsx
                            variable-trigger.tsx
                        global-variable-panel/
                            index.tsx
                            item.tsx
                        version-history-panel/
                            delete-confirm-modal.tsx
                            empty.tsx
                            index.tsx
                            restore-confirm-modal.tsx
                            version-history-item.tsx
                            context-menu/
                                index.tsx
                                menu-item.tsx
                                use-context-menu.ts
                            filter/
                                filter-item.tsx
                                filter-switch.tsx
                                index.tsx
                                use-filter.ts
                            loading/
                                index.tsx
                                item.tsx
                    plugin-dependency/
                        hooks.ts
                        index.tsx
                        store.ts
                    run/
                        hooks.ts
                        index.tsx
                        loop-result-panel.tsx
                        meta.tsx
                        node.tsx
                        output-panel.tsx
                        result-panel.tsx
                        result-text.tsx
                        special-result-panel.tsx
                        status-container.tsx
                        status.tsx
                        tracing-panel.tsx
                        agent-log/
                            agent-log-item.tsx
                            agent-log-nav-more.tsx
                            agent-log-nav.tsx
                            agent-log-trigger.tsx
                            agent-result-panel.tsx
                            index.tsx
                        assets/
                            bg-line-error.svg
                            bg-line-running.svg
                            bg-line-success.svg
                            bg-line-warning.svg
                            highlight-dark.svg
                            highlight.svg
                        iteration-log/
                            index.tsx
                            iteration-log-trigger.tsx
                            iteration-result-panel.tsx
                        loop-log/
                            index.tsx
                            loop-log-trigger.tsx
                            loop-result-panel.tsx
                        retry-log/
                            index.tsx
                            retry-log-trigger.tsx
                            retry-result-panel.tsx
                        utils/
                            format-log/
                                graph-to-log-struct.spec.ts
                                graph-to-log-struct.ts
                                index.ts
                                agent/
                                    data.ts
                                    index.spec.ts
                                    index.ts
                                iteration/
                                    index.spec.ts
                                    index.ts
                                loop/
                                    index.spec.ts
                                    index.ts
                                parallel/
                                    index.ts
                                retry/
                                    index.spec.ts
                                    index.ts
                    simple-node/
                        constants.ts
                        index.tsx
                        types.ts
                    store/
                        index.ts
                        workflow/
                            chat-variable-slice.ts
                            env-variable-slice.ts
                            form-slice.ts
                            help-line-slice.ts
                            history-slice.ts
                            index.ts
                            layout-slice.ts
                            node-slice.ts
                            panel-slice.ts
                            tool-slice.ts
                            version-slice.ts
                            workflow-draft-slice.ts
                            workflow-slice.ts
                            debug/
                                inspect-vars-slice.ts
                                mock-data.ts
                    utils/
                        common.ts
                        dagre-layout.ts
                        debug.ts
                        edge.ts
                        index.ts
                        node-navigation.ts
                        node.ts
                        tool.ts
                        variable.ts
                        workflow-init.spec.ts
                        workflow-init.ts
                        workflow.ts
                    variable-inspect/
                        empty.tsx
                        group.tsx
                        index.tsx
                        left.tsx
                        panel.tsx
                        right.tsx
                        trigger.tsx
                        types.ts
                        utils.tsx
                        value-content.tsx
                workflow-app/
                    index.tsx
                    components/
                        workflow-children.tsx
                        workflow-main.tsx
                        workflow-panel.tsx
                        workflow-header/
                            chat-variable-trigger.tsx
                            features-trigger.tsx
                            index.tsx
                    hooks/
                        index.ts
                        use-configs-map.ts
                        use-inspect-vars-crud.ts
                        use-is-chat-mode.ts
                        use-nodes-sync-draft.ts
                        use-workflow-init.ts
                        use-workflow-refresh-draft.ts
                        use-workflow-run.ts
                        use-workflow-start-run.tsx
                        use-workflow-template.ts
                    store/
                        workflow/
                            workflow-slice.ts
            education-apply/
                constants.ts
                education-apply-page.tsx
                hooks.ts
                role-selector.tsx
                search-input.tsx
                types.ts
                user-info.tsx
                verify-state-modal.tsx
            forgot-password/
                ChangePasswordForm.tsx
                ForgotPasswordForm.tsx
                page.tsx
            init/
                InitPasswordPopup.tsx
                page.tsx
            install/
                installForm.tsx
                page.tsx
            oauth-callback/
                page.tsx
            repos/
                [owner]/
                    [repo]/
                        releases/
                            route.ts
            reset-password/
                layout.tsx
                page.tsx
                check-code/
                    page.tsx
                set-password/
                    page.tsx
            signin/
                layout.tsx
                normal-form.tsx
                one-more-step.tsx
                page.module.css
                page.tsx
                _header.tsx
                assets/
                    github-dark.svg
                    github.svg
                    google.svg
                check-code/
                    page.tsx
                components/
                    mail-and-code-auth.tsx
                    mail-and-password-auth.tsx
                    social-auth.tsx
                    sso-auth.tsx
                invite-settings/
                    page.tsx
            styles/
                globals.css
                markdown.scss
                preflight.css
        assets/
            action.svg
            csv.svg
            delete.svg
            doc.svg
            docx.svg
            html.svg
            json.svg
            md.svg
            pdf.svg
            txt.svg
            xlsx.svg
        bin/
            uglify-embed.js
        config/
            index.spec.ts
            index.ts
        context/
            access-control-store.ts
            app-context.tsx
            dataset-detail.ts
            datasets-context.tsx
            debug-configuration.ts
            event-emitter.tsx
            explore-context.ts
            external-api-panel-context.tsx
            external-knowledge-api-context.tsx
            global-public-context.tsx
            i18n.ts
            mitt-context.tsx
            modal-context.tsx
            provider-context.tsx
            query-client.tsx
            web-app-context.tsx
            workspace-context.tsx
        docker/
            entrypoint.sh
            pm2.json
        hooks/
            use-app-favicon.ts
            use-breakpoints.spec.ts
            use-breakpoints.ts
            use-document-title.spec.ts
            use-document-title.ts
            use-format-time-from-now.ts
            use-i18n.ts
            use-import-dsl.ts
            use-knowledge.ts
            use-metadata.ts
            use-mitt.ts
            use-moderate.ts
            use-oauth.ts
            use-pay.tsx
            use-tab-searchparams.ts
            use-theme.ts
            use-timestamp.spec.ts
            use-timestamp.ts
        i18n/
            de-DE/
                app-annotation.ts
                app-api.ts
                app-debug.ts
                app-log.ts
                app-overview.ts
                app.ts
                billing.ts
                common.ts
                custom.ts
                dataset-creation.ts
                dataset-documents.ts
                dataset-hit-testing.ts
                dataset-settings.ts
                dataset.ts
                education.ts
                explore.ts
                layout.ts
                login.ts
                plugin-tags.ts
                plugin.ts
                register.ts
                run-log.ts
                share.ts
                time.ts
                tools.ts
                workflow.ts
            en-US/
                app-annotation.ts
                app-api.ts
                app-debug.ts
                app-log.ts
                app-overview.ts
                app.ts
                billing.ts
                common.ts
                custom.ts
                dataset-creation.ts
                dataset-documents.ts
                dataset-hit-testing.ts
                dataset-settings.ts
                dataset.ts
                education.ts
                explore.ts
                layout.ts
                login.ts
                plugin-tags.ts
                plugin.ts
                register.ts
                run-log.ts
                share.ts
                time.ts
                tools.ts
                workflow.ts
            es-ES/
                app-annotation.ts
                app-api.ts
                app-debug.ts
                app-log.ts
                app-overview.ts
                app.ts
                billing.ts
                common.ts
                custom.ts
                dataset-creation.ts
                dataset-documents.ts
                dataset-hit-testing.ts
                dataset-settings.ts
                dataset.ts
                education.ts
                explore.ts
                layout.ts
                login.ts
                plugin-tags.ts
                plugin.ts
                register.ts
                run-log.ts
                share.ts
                time.ts
                tools.ts
                workflow.ts
            fa-IR/
                app-annotation.ts
                app-api.ts
                app-debug.ts
                app-log.ts
                app-overview.ts
                app.ts
                billing.ts
                common.ts
                custom.ts
                dataset-creation.ts
                dataset-documents.ts
                dataset-hit-testing.ts
                dataset-settings.ts
                dataset.ts
                education.ts
                explore.ts
                layout.ts
                login.ts
                plugin-tags.ts
                plugin.ts
                register.ts
                run-log.ts
                share.ts
                time.ts
                tools.ts
                workflow.ts
            fr-FR/
                app-annotation.ts
                app-api.ts
                app-debug.ts
                app-log.ts
                app-overview.ts
                app.ts
                billing.ts
                common.ts
                custom.ts
                dataset-creation.ts
                dataset-documents.ts
                dataset-hit-testing.ts
                dataset-settings.ts
                dataset.ts
                education.ts
                explore.ts
                layout.ts
                login.ts
                plugin-tags.ts
                plugin.ts
                register.ts
                run-log.ts
                share.ts
                time.ts
                tools.ts
                workflow.ts
            hi-IN/
                app-annotation.ts
                app-api.ts
                app-debug.ts
                app-log.ts
                app-overview.ts
                app.ts
                billing.ts
                common.ts
                custom.ts
                dataset-creation.ts
                dataset-documents.ts
                dataset-hit-testing.ts
                dataset-settings.ts
                dataset.ts
                education.ts
                explore.ts
                layout.ts
                login.ts
                plugin-tags.ts
                plugin.ts
                register.ts
                run-log.ts
                share.ts
                time.ts
                tools.ts
                workflow.ts
            it-IT/
                app-annotation.ts
                app-api.ts
                app-debug.ts
                app-log.ts
                app-overview.ts
                app.ts
                billing.ts
                common.ts
                custom.ts
                dataset-creation.ts
                dataset-documents.ts
                dataset-hit-testing.ts
                dataset-settings.ts
                dataset.ts
                education.ts
                explore.ts
                layout.ts
                login.ts
                plugin-tags.ts
                plugin.ts
                register.ts
                run-log.ts
                share.ts
                time.ts
                tools.ts
                workflow.ts
            ja-JP/
                app-annotation.ts
                app-api.ts
                app-debug.ts
                app-log.ts
                app-overview.ts
                app.ts
                billing.ts
                common.ts
                custom.ts
                dataset-creation.ts
                dataset-documents.ts
                dataset-hit-testing.ts
                dataset-settings.ts
                dataset.ts
                education.ts
                explore.ts
                layout.ts
                login.ts
                plugin-tags.ts
                plugin.ts
                register.ts
                run-log.ts
                share.ts
                time.ts
                tools.ts
                workflow.ts
            ko-KR/
                app-annotation.ts
                app-api.ts
                app-debug.ts
                app-log.ts
                app-overview.ts
                app.ts
                billing.ts
                common.ts
                custom.ts
                dataset-creation.ts
                dataset-documents.ts
                dataset-hit-testing.ts
                dataset-settings.ts
                dataset.ts
                education.ts
                explore.ts
                layout.ts
                login.ts
                plugin-tags.ts
                plugin.ts
                register.ts
                run-log.ts
                share.ts
                time.ts
                tools.ts
                workflow.ts
            pl-PL/
                app-annotation.ts
                app-api.ts
                app-debug.ts
                app-log.ts
                app-overview.ts
                app.ts
                billing.ts
                common.ts
                custom.ts
                dataset-creation.ts
                dataset-documents.ts
                dataset-hit-testing.ts
                dataset-settings.ts
                dataset.ts
                education.ts
                explore.ts
                layout.ts
                login.ts
                plugin-tags.ts
                plugin.ts
                register.ts
                run-log.ts
                share.ts
                time.ts
                tools.ts
                workflow.ts
            pt-BR/
                app-annotation.ts
                app-api.ts
                app-debug.ts
                app-log.ts
                app-overview.ts
                app.ts
                billing.ts
                common.ts
                custom.ts
                dataset-creation.ts
                dataset-documents.ts
                dataset-hit-testing.ts
                dataset-settings.ts
                dataset.ts
                education.ts
                explore.ts
                layout.ts
                login.ts
                plugin-tags.ts
                plugin.ts
                register.ts
                run-log.ts
                share.ts
                time.ts
                tools.ts
                workflow.ts
            ro-RO/
                app-annotation.ts
                app-api.ts
                app-debug.ts
                app-log.ts
                app-overview.ts
                app.ts
                billing.ts
                common.ts
                custom.ts
                dataset-creation.ts
                dataset-documents.ts
                dataset-hit-testing.ts
                dataset-settings.ts
                dataset.ts
                education.ts
                explore.ts
                layout.ts
                login.ts
                plugin-tags.ts
                plugin.ts
                register.ts
                run-log.ts
                share.ts
                time.ts
                tools.ts
                workflow.ts
            ru-RU/
                app-annotation.ts
                app-api.ts
                app-debug.ts
                app-log.ts
                app-overview.ts
                app.ts
                billing.ts
                common.ts
                custom.ts
                dataset-creation.ts
                dataset-documents.ts
                dataset-hit-testing.ts
                dataset-settings.ts
                dataset.ts
                education.ts
                explore.ts
                layout.ts
                login.ts
                plugin-tags.ts
                plugin.ts
                register.ts
                run-log.ts
                share.ts
                time.ts
                tools.ts
                workflow.ts
            sl-SI/
                app-annotation.ts
                app-api.ts
                app-debug.ts
                app-log.ts
                app-overview.ts
                app.ts
                billing.ts
                common.ts
                custom.ts
                dataset-creation.ts
                dataset-documents.ts
                dataset-hit-testing.ts
                dataset-settings.ts
                dataset.ts
                education.ts
                explore.ts
                layout.ts
                login.ts
                plugin-tags.ts
                plugin.ts
                register.ts
                run-log.ts
                share.ts
                time.ts
                tools.ts
                workflow.ts
            th-TH/
                app-annotation.ts
                app-api.ts
                app-debug.ts
                app-log.ts
                app-overview.ts
                app.ts
                billing.ts
                common.ts
                custom.ts
                dataset-creation.ts
                dataset-documents.ts
                dataset-hit-testing.ts
                dataset-settings.ts
                dataset.ts
                education.ts
                explore.ts
                layout.ts
                login.ts
                plugin-tags.ts
                plugin.ts
                register.ts
                run-log.ts
                share.ts
                time.ts
                tools.ts
                workflow.ts
            tr-TR/
                app-annotation.ts
                app-api.ts
                app-debug.ts
                app-log.ts
                app-overview.ts
                app.ts
                billing.ts
                common.ts
                custom.ts
                dataset-creation.ts
                dataset-documents.ts
                dataset-hit-testing.ts
                dataset-settings.ts
                dataset.ts
                education.ts
                explore.ts
                layout.ts
                login.ts
                plugin-tags.ts
                plugin.ts
                register.ts
                run-log.ts
                share.ts
                time.ts
                tools.ts
                workflow.ts
            uk-UA/
                app-annotation.ts
                app-api.ts
                app-debug.ts
                app-log.ts
                app-overview.ts
                app.ts
                billing.ts
                common.ts
                custom.ts
                dataset-creation.ts
                dataset-documents.ts
                dataset-hit-testing.ts
                dataset-settings.ts
                dataset.ts
                education.ts
                explore.ts
                layout.ts
                login.ts
                plugin-tags.ts
                plugin.ts
                register.ts
                run-log.ts
                share.ts
                time.ts
                tools.ts
                workflow.ts
            vi-VN/
                app-annotation.ts
                app-api.ts
                app-debug.ts
                app-log.ts
                app-overview.ts
                app.ts
                billing.ts
                common.ts
                custom.ts
                dataset-creation.ts
                dataset-documents.ts
                dataset-hit-testing.ts
                dataset-settings.ts
                dataset.ts
                education.ts
                explore.ts
                layout.ts
                login.ts
                plugin-tags.ts
                plugin.ts
                register.ts
                run-log.ts
                share.ts
                time.ts
                tools.ts
                workflow.ts
            zh-Hans/
                app-annotation.ts
                app-api.ts
                app-debug.ts
                app-log.ts
                app-overview.ts
                app.ts
                billing.ts
                common.ts
                custom.ts
                dataset-creation.ts
                dataset-documents.ts
                dataset-hit-testing.ts
                dataset-settings.ts
                dataset.ts
                education.ts
                explore.ts
                layout.ts
                login.ts
                plugin-tags.ts
                plugin.ts
                register.ts
                run-log.ts
                share.ts
                time.ts
                tools.ts
                workflow.ts
            zh-Hant/
                app-annotation.ts
                app-api.ts
                app-debug.ts
                app-log.ts
                app-overview.ts
                app.ts
                billing.ts
                common.ts
                custom.ts
                dataset-creation.ts
                dataset-documents.ts
                dataset-hit-testing.ts
                dataset-settings.ts
                dataset.ts
                education.ts
                explore.ts
                layout.ts
                login.ts
                plugin-tags.ts
                plugin.ts
                register.ts
                run-log.ts
                share.ts
                time.ts
                tools.ts
                workflow.ts
        i18n-config/
            auto-gen-i18n.js
            check-i18n.js
            DEV.md
            i18next-config.ts
            index.ts
            language.ts
            languages.json
            README.md
            server.ts
        models/
            access-control.ts
            app.ts
            common.ts
            datasets.ts
            debug.ts
            explore.ts
            log.ts
            share.ts
            user.ts
        public/
            embed.js
            embed.min.js
            favicon.ico
            pdf.worker.min.mjs
            education/
                bg.png
            logo/
                logo-embedded-chat-avatar.png
                logo-embedded-chat-header.png
                <EMAIL>
                <EMAIL>
                logo-monochrome-white.svg
                logo-site-dark.png
                logo-site.png
                logo.svg
            screenshots/
                dark/
                    Agent.png
                    <EMAIL>
                    <EMAIL>
                    Chatbot.png
                    <EMAIL>
                    <EMAIL>
                    Chatflow.png
                    <EMAIL>
                    <EMAIL>
                    TextGenerator.png
                    <EMAIL>
                    <EMAIL>
                    Workflow.png
                    <EMAIL>
                    <EMAIL>
                light/
                    Agent.png
                    <EMAIL>
                    <EMAIL>
                    Chatbot.png
                    <EMAIL>
                    <EMAIL>
                    Chatflow.png
                    <EMAIL>
                    <EMAIL>
                    TextGenerator.png
                    <EMAIL>
                    <EMAIL>
                    Workflow.png
                    <EMAIL>
                    <EMAIL>
            vs/
                loader.js
                base/
                    browser/
                        ui/
                            codicons/
                                codicon/
                                    codicon.ttf
                    common/
                        worker/
                            simpleWorker.nls.de.js
                            simpleWorker.nls.es.js
                            simpleWorker.nls.fr.js
                            simpleWorker.nls.it.js
                            simpleWorker.nls.ja.js
                            simpleWorker.nls.js
                            simpleWorker.nls.ko.js
                            simpleWorker.nls.ru.js
                            simpleWorker.nls.zh-cn.js
                            simpleWorker.nls.zh-tw.js
                    worker/
                        workerMain.js
                basic-languages/
                    abap/
                        abap.js
                    apex/
                        apex.js
                    azcli/
                        azcli.js
                    bat/
                        bat.js
                    bicep/
                        bicep.js
                    cameligo/
                        cameligo.js
                    clojure/
                        clojure.js
                    coffee/
                        coffee.js
                    cpp/
                        cpp.js
                    csharp/
                        csharp.js
                    csp/
                        csp.js
                    css/
                        css.js
                    cypher/
                        cypher.js
                    dart/
                        dart.js
                    dockerfile/
                        dockerfile.js
                    ecl/
                        ecl.js
                    elixir/
                        elixir.js
                    flow9/
                        flow9.js
                    freemarker2/
                        freemarker2.js
                    fsharp/
                        fsharp.js
                    go/
                        go.js
                    graphql/
                        graphql.js
                    handlebars/
                        handlebars.js
                    hcl/
                        hcl.js
                    html/
                        html.js
                    ini/
                        ini.js
                    java/
                        java.js
                    javascript/
                        javascript.js
                    julia/
                        julia.js
                    kotlin/
                        kotlin.js
                    less/
                        less.js
                    lexon/
                        lexon.js
                    liquid/
                        liquid.js
                    lua/
                        lua.js
                    m3/
                        m3.js
                    markdown/
                        markdown.js
                    mdx/
                        mdx.js
                    mips/
                        mips.js
                    msdax/
                        msdax.js
                    mysql/
                        mysql.js
                    objective-c/
                        objective-c.js
                    pascal/
                        pascal.js
                    pascaligo/
                        pascaligo.js
                    perl/
                        perl.js
                    pgsql/
                        pgsql.js
                    php/
                        php.js
                    pla/
                        pla.js
                    postiats/
                        postiats.js
                    powerquery/
                        powerquery.js
                    powershell/
                        powershell.js
                    protobuf/
                        protobuf.js
                    pug/
                        pug.js
                    python/
                        python.js
                    qsharp/
                        qsharp.js
                    r/
                        r.js
                    razor/
                        razor.js
                    redis/
                        redis.js
                    redshift/
                        redshift.js
                    restructuredtext/
                        restructuredtext.js
                    ruby/
                        ruby.js
                    rust/
                        rust.js
                    sb/
                        sb.js
                    scala/
                        scala.js
                    scheme/
                        scheme.js
                    scss/
                        scss.js
                    shell/
                        shell.js
                    solidity/
                        solidity.js
                    sophia/
                        sophia.js
                    sparql/
                        sparql.js
                    sql/
                        sql.js
                    st/
                        st.js
                    swift/
                        swift.js
                    systemverilog/
                        systemverilog.js
                    tcl/
                        tcl.js
                    twig/
                        twig.js
                    typescript/
                        typescript.js
                    vb/
                        vb.js
                    wgsl/
                        wgsl.js
                    xml/
                        xml.js
                    yaml/
                        yaml.js
                editor/
                    editor.main.css
                    editor.main.js
                    editor.main.nls.de.js
                    editor.main.nls.es.js
                    editor.main.nls.fr.js
                    editor.main.nls.it.js
                    editor.main.nls.ja.js
                    editor.main.nls.js
                    editor.main.nls.ko.js
                    editor.main.nls.ru.js
                    editor.main.nls.zh-cn.js
                    editor.main.nls.zh-tw.js
                language/
                    css/
                        cssMode.js
                        cssWorker.js
                    html/
                        htmlMode.js
                        htmlWorker.js
                    json/
                        jsonMode.js
                        jsonWorker.js
                    typescript/
                        tsMode.js
                        tsWorker.js
        service/
            access-control.ts
            annotation.ts
            apps.ts
            base.ts
            billing.ts
            common.ts
            datasets.ts
            debug.ts
            explore.ts
            fetch.ts
            log.ts
            plugins.ts
            refresh-token.ts
            share.ts
            sso.ts
            strategy.ts
            tag.ts
            tools.ts
            use-apps.ts
            use-base.ts
            use-common.ts
            use-education.ts
            use-endpoints.ts
            use-explore.ts
            use-models.ts
            use-plugins-auth.ts
            use-plugins.ts
            use-share.ts
            use-strategy.ts
            use-tools.ts
            use-workflow.ts
            workflow.ts
            _tools_util.spec.ts
            _tools_util.ts
            demo/
                index.tsx
            knowledge/
                use-create-dataset.ts
                use-document.ts
                use-hit-testing.ts
                use-import.ts
                use-metadata.ts
                use-segment.ts
        themes/
            dark.css
            light.css
            manual-dark.css
            manual-light.css
            markdown-dark.css
            markdown-light.css
            tailwind-theme-var-define.ts
        types/
            app.ts
            feature.ts
            workflow.ts
        utils/
            app-redirection.ts
            classnames.spec.ts
            classnames.ts
            clipboard.ts
            completion-params.ts
            context.ts
            emoji.ts
            format.spec.ts
            format.ts
            get-icon.ts
            index.spec.ts
            index.ts
            model-config.ts
            navigation.ts
            permission.ts
            plugin-version-feature.spec.ts
            plugin-version-feature.ts
            semver.spec.ts
            semver.ts
            time.ts
            timezone.json
            timezone.ts
            tool-call.ts
            var-basePath.js
            var.ts
            zod.spec.ts
        __mocks__/
            mime.js
        __tests__/
            check-i18n.test.ts
            description-validation.test.tsx
            document-detail-navigation-fix.test.tsx
            document-list-sorting.test.tsx
            i18n-upload-features.test.ts
            navigation-utils.test.ts
            plugin-tool-workflow-error.test.tsx
            real-browser-flicker.test.tsx
            unified-tags-logic.test.ts
            workflow-parallel-limit.test.tsx
            xss-fix-verification.test.tsx
            goto-anything/
                command-selector.test.tsx
                search-error-handling.test.ts
--- End of tree view from C:\Users\<USER>\Desktop\dify-main ---
2025-08-19 15:49:25,930 - INFO - HTTP Request: POST http://**************:8301/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-19 15:49:33,831 - INFO - 📞 工具调用: NewTask 参数: {
  "task_description": "初始化项目并判断框架类型，分析项目整体架构",
  "Additional_information": "项目路径：C:\\Users\\<USER>\\Desktop\\dify-main\n这是一个AI应用开发平台项目，包含前端（web）和后端（api）部分。需要分析：\n1. 项目的整体架构\n2. 前端框架和技术栈\n3. 后端框架和技术栈\n4. 数据库类型\n5. 主要功能模块划分\n6. 项目入口点识别\n\n请重点关注：\n- 前端：查看package.json、next.config.js等配置文件\n- 后端：查看app.py、requirements.txt等配置文件\n- 数据库：查看migrations文件夹和相关配置\n- 入口点：识别主要的应用入口文件和路由配置\n\n需要详细列出所有发现的技术栈和架构信息。"
}
2025-08-19 15:49:33,831 - INFO - TOKEN_USAGE - Model: glm-4.5, Prompt: 43827, Completion: 404, Total: 44231
2025-08-19 15:49:33,862 - INFO - NewTask called with: 初始化项目并判断框架类型，分析项目整体架构
2025-08-19 15:49:33,862 - INFO - 
📋 [TaskStack] 添加单个任务
2025-08-19 15:49:33,864 - INFO -    详情: 任务: 初始化项目并判断框架类型，分析项目整体架构
2025-08-19 15:49:33,864 - INFO -    📚 当前任务树 (共 1 个任务):
2025-08-19 15:49:33,864 - INFO - └── 🔄 初始化项目并判断框架类型，分析项目整体架构 (ID: a50d77ba)  <== [正在执行]
2025-08-19 15:49:33,864 - INFO - 
2025-08-19 15:49:33,864 - INFO - 🧠 正在为新任务 '初始化项目并判断框架类型，分析项目整体架构...' 检索历史经验...
2025-08-19 15:49:34,220 - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/embeddings "HTTP/1.1 200 OK"
2025-08-19 15:49:34,249 - INFO - 📂 未找到相关历史记录。
2025-08-19 15:49:38,536 - INFO - HTTP Request: POST http://**************:8301/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-19 15:49:41,987 - INFO - 📞 工具调用: list_directory 参数: {
  "directory_path": "C:\\Users\\<USER>\\Desktop\\dify-main"
}
2025-08-19 15:49:41,987 - INFO - TOKEN_USAGE - Model: glm-4.5, Prompt: 10518, Completion: 181, Total: 10699
2025-08-19 15:49:45,376 - INFO - HTTP Request: POST http://**************:8301/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-19 15:49:46,955 - INFO - 📞 工具调用: read_file 参数: {
  "file_path": "C:\\Users\\<USER>\\Desktop\\dify-main\\package.json"
}
2025-08-19 15:49:46,955 - INFO - TOKEN_USAGE - Model: glm-4.5, Prompt: 10834, Completion: 97, Total: 10931
2025-08-19 15:49:51,287 - INFO - HTTP Request: POST http://**************:8301/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-19 15:49:52,600 - INFO - 📞 工具调用: list_directory 参数: {
  "directory_path": "C:\\Users\\<USER>\\Desktop\\dify-main\\web"
}
2025-08-19 15:49:52,600 - INFO - TOKEN_USAGE - Model: glm-4.5, Prompt: 11007, Completion: 74, Total: 11081
2025-08-19 15:49:55,564 - INFO - HTTP Request: POST http://**************:8301/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-19 15:49:56,374 - INFO - 📞 工具调用: read_file 参数: {
  "file_path": "C:\\Users\\<USER>\\Desktop\\dify-main\\web\\package.json"
}
2025-08-19 15:49:56,374 - INFO - TOKEN_USAGE - Model: glm-4.5, Prompt: 11286, Completion: 49, Total: 11335
2025-08-19 15:49:58,935 - INFO - HTTP Request: POST http://**************:8301/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-19 15:50:00,399 - INFO - 📞 工具调用: read_file 参数: {
  "file_path": "C:\\Users\\<USER>\\Desktop\\dify-main\\web\\next.config.js"
}
2025-08-19 15:50:00,399 - INFO - TOKEN_USAGE - Model: glm-4.5, Prompt: 15670, Completion: 83, Total: 15753
2025-08-19 15:50:03,967 - INFO - HTTP Request: POST http://**************:8301/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-19 15:50:05,218 - INFO - 📞 工具调用: list_directory 参数: {
  "directory_path": "C:\\Users\\<USER>\\Desktop\\dify-main\\api"
}
2025-08-19 15:50:05,218 - INFO - TOKEN_USAGE - Model: glm-4.5, Prompt: 16616, Completion: 71, Total: 16687
2025-08-19 15:50:08,310 - INFO - HTTP Request: POST http://**************:8301/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-19 15:50:09,091 - INFO - 📞 工具调用: read_file 参数: {
  "file_path": "C:\\Users\\<USER>\\Desktop\\dify-main\\api\\app.py"
}
2025-08-19 15:50:09,091 - INFO - TOKEN_USAGE - Model: glm-4.5, Prompt: 16857, Completion: 51, Total: 16908
2025-08-19 15:50:20,183 - INFO - HTTP Request: POST http://**************:8301/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-19 15:50:20,809 - INFO - 📞 工具调用: read_file 参数: {
  "file_path": "C:\\Users\\<USER>\\Desktop\\dify-main\\api\\app_factory.py"
}
2025-08-19 15:50:20,809 - INFO - TOKEN_USAGE - Model: glm-4.5, Prompt: 17338, Completion: 49, Total: 17387
2025-08-19 15:50:24,202 - INFO - HTTP Request: POST http://**************:8301/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-19 15:50:25,145 - INFO - 📞 工具调用: read_file 参数: {
  "file_path": "C:\\Users\\<USER>\\Desktop\\dify-main\\api\\pyproject.toml"
}
2025-08-19 15:50:25,145 - INFO - TOKEN_USAGE - Model: glm-4.5, Prompt: 18314, Completion: 74, Total: 18388
2025-08-19 15:50:30,420 - INFO - HTTP Request: POST http://**************:8301/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-19 15:50:31,358 - INFO - 📞 工具调用: list_directory 参数: {
  "directory_path": "C:\\Users\\<USER>\\Desktop\\dify-main\\api\\migrations"
}
2025-08-19 15:50:31,358 - INFO - TOKEN_USAGE - Model: glm-4.5, Prompt: 21352, Completion: 53, Total: 21405
2025-08-19 15:50:35,968 - INFO - HTTP Request: POST http://**************:8301/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-19 15:50:38,308 - INFO - 📞 工具调用: list_directory 参数: {
  "directory_path": "C:\\Users\\<USER>\\Desktop\\dify-main\\api\\migrations\\versions"
}
2025-08-19 15:50:38,308 - INFO - TOKEN_USAGE - Model: glm-4.5, Prompt: 21474, Completion: 48, Total: 21522
2025-08-19 15:50:41,360 - INFO - HTTP Request: POST http://**************:8301/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-19 15:50:43,823 - INFO - 📞 工具调用: list_directory 参数: {
  "directory_path": "C:\\Users\\<USER>\\Desktop\\dify-main\\api\\configs"
}
2025-08-19 15:50:43,824 - INFO - TOKEN_USAGE - Model: glm-4.5, Prompt: 24396, Completion: 49, Total: 24445
2025-08-19 15:50:48,791 - INFO - HTTP Request: POST http://**************:8301/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-19 15:50:49,822 - INFO - 📞 工具调用: read_file 参数: {
  "file_path": "C:\\Users\\<USER>\\Desktop\\dify-main\\api\\configs\\app_config.py",
  "start_line": 1,
  "end_line": 100
}
2025-08-19 15:50:49,822 - INFO - TOKEN_USAGE - Model: glm-4.5, Prompt: 24528, Completion: 66, Total: 24594
2025-08-19 15:50:54,232 - INFO - HTTP Request: POST http://**************:8301/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-19 15:50:54,786 - INFO - 📞 工具调用: read_file 参数: {
  "file_path": "C:\\Users\\<USER>\\Desktop\\dify-main\\api\\configs\\app_config.py",
  "start_line": 100
}
2025-08-19 15:50:54,787 - INFO - TOKEN_USAGE - Model: glm-4.5, Prompt: 25590, Completion: 57, Total: 25647
2025-08-19 15:50:58,659 - INFO - HTTP Request: POST http://**************:8301/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-19 15:50:59,511 - INFO - 📞 工具调用: list_directory 参数: {
  "directory_path": "C:\\Users\\<USER>\\Desktop\\dify-main\\api\\configs\\deploy"
}
2025-08-19 15:50:59,511 - INFO - TOKEN_USAGE - Model: glm-4.5, Prompt: 25812, Completion: 48, Total: 25860
2025-08-19 15:51:04,732 - INFO - HTTP Request: POST http://**************:8301/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-19 15:51:05,206 - INFO - 📞 工具调用: read_file 参数: {
  "file_path": "C:\\Users\\<USER>\\Desktop\\dify-main\\api\\configs\\deploy\\__init__.py"
}
2025-08-19 15:51:05,206 - INFO - TOKEN_USAGE - Model: glm-4.5, Prompt: 25915, Completion: 46, Total: 25961
2025-08-19 15:51:10,197 - INFO - HTTP Request: POST http://**************:8301/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-19 15:51:10,918 - INFO - 📞 工具调用: list_directory 参数: {
  "directory_path": "C:\\Users\\<USER>\\Desktop\\dify-main\\api\\configs\\extra"
}
2025-08-19 15:51:10,920 - INFO - TOKEN_USAGE - Model: glm-4.5, Prompt: 26301, Completion: 47, Total: 26348
2025-08-19 15:51:14,660 - INFO - HTTP Request: POST http://**************:8301/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-19 15:51:15,138 - INFO - 📞 工具调用: read_file 参数: {
  "file_path": "C:\\Users\\<USER>\\Desktop\\dify-main\\api\\configs\\extra\\__init__.py"
}
2025-08-19 15:51:15,138 - INFO - TOKEN_USAGE - Model: glm-4.5, Prompt: 26414, Completion: 46, Total: 26460
2025-08-19 15:51:19,258 - INFO - HTTP Request: POST http://**************:8301/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-19 15:51:19,996 - INFO - 📞 工具调用: read_file 参数: {
  "file_path": "C:\\Users\\<USER>\\Desktop\\dify-main\\api\\.env.example"
}
2025-08-19 15:51:19,996 - INFO - TOKEN_USAGE - Model: glm-4.5, Prompt: 26591, Completion: 49, Total: 26640
2025-08-19 15:51:24,853 - INFO - HTTP Request: POST http://**************:8301/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-19 15:51:25,650 - INFO - 📞 工具调用: list_directory 参数: {
  "directory_path": "C:\\Users\\<USER>\\Desktop\\dify-main\\web\\app"
}
2025-08-19 15:51:25,650 - INFO - TOKEN_USAGE - Model: glm-4.5, Prompt: 32683, Completion: 46, Total: 32729
2025-08-19 15:51:31,558 - INFO - HTTP Request: POST http://**************:8301/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-19 15:51:32,215 - INFO - 📞 工具调用: read_file 参数: {
  "file_path": "C:\\Users\\<USER>\\Desktop\\dify-main\\web\\app\\page.tsx"
}
2025-08-19 15:51:32,215 - INFO - TOKEN_USAGE - Model: glm-4.5, Prompt: 32848, Completion: 43, Total: 32891
2025-08-19 15:51:38,181 - INFO - HTTP Request: POST http://**************:8301/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-19 15:51:38,887 - INFO - 📞 工具调用: read_file 参数: {
  "file_path": "C:\\Users\\<USER>\\Desktop\\dify-main\\web\\app\\layout.tsx"
}
2025-08-19 15:51:38,887 - INFO - TOKEN_USAGE - Model: glm-4.5, Prompt: 33104, Completion: 48, Total: 33152
2025-08-19 15:51:44,595 - INFO - HTTP Request: POST http://**************:8301/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-19 15:51:45,349 - INFO - 📞 工具调用: list_directory 参数: {
  "directory_path": "C:\\Users\\<USER>\\Desktop\\dify-main\\web\\app\\components"
}
2025-08-19 15:51:45,351 - INFO - TOKEN_USAGE - Model: glm-4.5, Prompt: 34319, Completion: 49, Total: 34368
2025-08-19 15:51:50,511 - INFO - HTTP Request: POST http://**************:8301/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-19 15:51:53,113 - INFO - 📞 工具调用: list_directory 参数: {
  "directory_path": "C:\\Users\\<USER>\\Desktop\\dify-main\\api\\controllers"
}
2025-08-19 15:51:53,113 - INFO - TOKEN_USAGE - Model: glm-4.5, Prompt: 34516, Completion: 41, Total: 34557
2025-08-19 15:51:58,868 - INFO - HTTP Request: POST http://**************:8301/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-19 15:51:59,482 - INFO - 📞 工具调用: list_directory 参数: {
  "directory_path": "C:\\Users\\<USER>\\Desktop\\dify-main\\api\\services"
}
2025-08-19 15:51:59,483 - INFO - TOKEN_USAGE - Model: glm-4.5, Prompt: 34630, Completion: 40, Total: 34670
2025-08-19 15:52:06,149 - INFO - HTTP Request: POST http://**************:8301/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-19 15:52:06,953 - INFO - 📞 工具调用: list_directory 参数: {
  "directory_path": "C:\\Users\\<USER>\\Desktop\\dify-main\\api\\models"
}
2025-08-19 15:52:06,953 - INFO - TOKEN_USAGE - Model: glm-4.5, Prompt: 34969, Completion: 44, Total: 35013
2025-08-19 15:52:13,001 - INFO - HTTP Request: POST http://**************:8301/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-19 15:52:39,917 - INFO - 📞 工具调用: create_report 参数: {
  "title": "Dify项目架构分析报告",
  "report_content": "# Dify项目架构分析报告\n\n## 项目概述\n\nDify是一个AI应用开发平台，采用前后端分离的架构设计，主要用于构建、部署和管理AI驱动的应用程序。项目名称为\"dify\"，版本号为1.7.2。\n\n## 整体架构\n\n项目采用典型的前后端分离架构：\n- **前端**：位于`web`目录，基于Next.js框架构建\n- **后端**：位于`api`目录，基于Flask框架构建\n- **数据存储**：支持多种数据库和向量数据库\n- **容器化**：提供Docker支持，位于`docker`目录\n\n## 前端技术栈\n\n### 核心框架\n- **Next.js**：版本15.3.5，作为React全栈框架\n- **React**：版本19.1.0，作为UI库\n- **TypeScript**：版本5.8.3，提供类型安全\n- **TailwindCSS**：版本3.4.14，作为CSS框架\n\n### 关键依赖\n- **状态管理**：Zustand (4.5.2) 和 Tanstack/react-query (5.60.5)\n- **UI组件**：Headless UI (2.2.1), Heroicons (2.0.16), Remix Icon (4.5.0)\n- **富文本编辑**：Lexical (0.30.0) 和 Monaco Editor (4.6.0)\n- **图表可视化**：ECharts (5.5.1) 和 Mermaid (11.4.1)\n- **国际化**：i18next (23.16.4) 和 react-i18next (15.1.0)\n- **表单处理**：React Hook Form (7.53.1) 和 Zod (3.23.8)\n- **工作流可视化**：React Flow (11.11.3)\n\n### 开发工具\n- **代码检查**：ESLint (9.32.0) 和 Oxlint\n- **测试**：Jest (29.7.0) 和 React Testing Library\n- **构建工具**：支持包分析、代码压缩等\n\n### 项目结构\n前端采用Next.js 13+的App Router模式，主要目录包括：\n- `app/`：应用路由和页面\n- `components/`：可复用组件\n- `context/`：React Context\n- `hooks/`：自定义Hooks\n- `service/`：API服务\n- `utils/`：工具函数\n- `i18n/`：国际化配置\n\n## 后端技术栈\n\n### 核心框架\n- **Flask**：版本3.1.0，作为Web框架\n- **SQLAlchemy**：版本2.0.29，作为ORM\n- **Pydantic**：版本2.11.4，用于数据验证和设置管理\n- **Alembic**：用于数据库迁移\n\n### 关键依赖\n- **异步处理**：Gevent (24.11.1) 和 Celery (5.5.2)\n- **任务队列**：Redis (6.1.0)\n- **API框架**：Flask-RESTful (0.3.10)\n- **认证**：Flask-Login (0.6.3) 和 Authlib (1.3.1)\n- **AI模型集成**：OpenAI (1.61.0), Transformers (4.51.0), LangSmith (0.1.77)\n- **文档处理**：Unstructured (0.16.1), python-docx (1.1.0), PyPDFium2 (4.30.0)\n- **监控**：Sentry (2.28.0), OpenTelemetry (1.27.0)\n\n### 项目结构\n后端采用模块化设计，主要目录包括：\n- `controllers/`：API控制器\n- `services/`：业务逻辑服务\n- `models/`：数据模型\n- `extensions/`：Flask扩展\n- `repositories/`：数据访问层\n- `tasks/`：Celery任务\n- `events/`：事件处理\n- `contexts/`：上下文管理\n\n## 数据库技术栈\n\n### 主数据库\n- **PostgreSQL**：作为主数据库，通过环境变量配置连接\n- **SQLAlchemy**：作为ORM，提供数据库抽象\n- **Alembic**：处理数据库迁移，迁移文件位于`migrations/versions/`目录\n\n### 向量数据库\n支持多种向量数据库，包括：\n- Weaviate\n- Qdrant\n- Milvus\n- Chroma\n- Pinecone\n- Elasticsearch\n- OpenSearch\n- PGVector\n- 以及其他多种向量数据库选项\n\n### 缓存\n- **Redis**：用于缓存、会话存储和Celery消息代理\n\n## 主要功能模块\n\n### 前端模块\n1. **应用管理**：创建、配置和管理AI应用\n2. **工作流编辑器**：可视化工作流设计和编辑\n3. **数据集管理**：知识库和数据集管理\n4. **工具集成**：集成和管理各种AI工具\n5. **对话界面**：与AI模型交互的聊天界面\n6. **用户管理**：账户和权限管理\n\n### 后端模块\n1. **应用服务**：应用创建、配置和部署\n2. **对话服务**：处理用户与AI的对话\n3. **数据集服务**：文档处理和知识库管理\n4. **模型服务**：AI模型集成和管理\n5. **工作流服务**：工作流执行和管理\n6. **工具服务**：外部工具集成和调用\n7. **认证服务**：用户认证和授权\n\n## 项目入口点\n\n### 前端入口\n- **主入口**：`web/app/page.tsx` - 应用首页\n- **布局入口**：`web/app/layout.tsx` - 应用整体布局\n- **路由配置**：Next.js App Router，基于文件系统的路由\n\n### 后端入口\n- **主入口**：`api/app.py` - Flask应用启动点\n- **应用工厂**：`api/app_factory.py` - 应用创建和配置\n- **WSGI服务器**：使用Gunicorn作为生产服务器\n\n## 部署和运行\n\n### 开发环境\n- 前端：`pnpm dev` 启动开发服务器（端口3000）\n- 后端：`python app.py` 启动Flask应用（端口5001）\n\n### 生产环境\n- 前端：`pnpm build` 构建生产版本\n- 后端：使用Gunicorn作为WSGI服务器\n- 支持：Docker容器化部署\n\n## 总结\n\nDify是一个功能完善的AI应用开发平台，采用现代化的技术栈和架构设计。前后端分离的架构使其具有良好的可扩展性和可维护性。丰富的功能模块和灵活的配置选项使其能够适应各种AI应用场景。支持多种数据库和向量数据库的集成，为AI应用提供了强大的数据存储和检索能力。整体架构设计合理，代码组织清晰，是一个高质量的开源AI应用开发平台。"
}
2025-08-19 15:52:39,917 - INFO - TOKEN_USAGE - Model: glm-4.5, Prompt: 35126, Completion: 1586, Total: 36712
2025-08-19 15:52:46,517 - INFO - HTTP Request: POST http://**************:8301/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-19 15:52:54,890 - INFO - TOKEN_USAGE - Model: glm-4.5, Prompt: 36751, Completion: 499, Total: 37250
2025-08-19 15:52:56,014 - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/embeddings "HTTP/1.1 200 OK"
2025-08-19 15:52:56,184 - INFO - 📚 正在分批添加历史记录... 已处理 28 / 28 个片段。
2025-08-19 15:52:56,184 - INFO - ✅ 交互历史已全部分批添加完成（共 28 个片段）。
2025-08-19 15:52:56,185 - INFO - ✅ 子任务交互已成功存入向量化历史记录。
2025-08-19 15:52:56,185 - INFO - 
📋 [TaskStack] ✅ 任务完成并出栈
2025-08-19 15:52:56,185 - INFO -    详情: 任务: 初始化项目并判断框架类型，分析项目整体架构
2025-08-19 15:52:56,185 - INFO -    📚 当前任务树 (共 1 个任务):
2025-08-19 15:52:56,185 - INFO - └── ✅ 初始化项目并判断框架类型，分析项目整体架构 (ID: a50d77ba)
2025-08-19 15:52:56,185 - INFO - 
2025-08-19 15:52:56,185 - INFO - ✨工具输出: NewTask:
结果:
子任务执行结果：

我已经完成了对Dify项目的全面架构分析，并生成了一份详细的报告。报告内容包括：

1. **项目概述**：Dify是一个AI应用开发平台，版本1.7.2，采用前后端分离架构。

2. **整体架构**：前端(web目录)基于Next.js，后端(api目录)基于Flask，支持多种数据库和容器化部署。

3. **前端技术栈**：
   - 核心框架：Next.js 15.3.5、React 19.1.0、TypeScript 5.8.3
   - UI框架：TailwindCSS 3.4.14
   - 状态管理：Zustand和Tanstack/react-query
   - 富文本编辑：Lexical和Monaco Editor
   - 图表可视化：ECharts和Mermaid
   - 国际化：i18next
   - 项目结构采用Next.js 13+的App Router模式

4. **后端技术栈**：
   - 核心框架：Flask 3.1.0、SQLAlchemy 2.0.29、Pydantic 2.11.4
   - 异步处理：Gevent和Celery
   - 任务队列：Redis
   - AI模型集成：OpenAI、Transformers、LangSmith
   - 项目结构采用模块化设计，包括控制器、服务、模型等

5. **数据库技术栈**：
   - 主数据库：PostgreSQL
   - 向量数据库：支持Weaviate、Qdrant、Milvus、Chroma等多种
   - 缓存：Redis

6. **主要功能模块**：
   - 前端：应用管理、工作流编辑器、数据集管理、工具集成、对话界面、用户管理
   - 后端：应用服务、对话服务、数据集服务、模型服务、工作流服务、工具服务、认证服务

7. **项目入口点**：
   - 前端：web/app/page.tsx和web/app/layout.tsx
   - 后端：api/app.py和api/app_factory.py

8. **部署和运行**：支持开发环境和生产环境的不同部署方式，以及Docker容器化部署。

这份报告全面分析了Dify项目的技术架构、框架类型和整体设计，有助于理解项目结构和技术选型。
2025-08-19 15:53:05,188 - INFO - HTTP Request: POST http://**************:8301/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-19 15:53:12,692 - INFO - 📞 工具调用: NewTask 参数: {
  "task_description": "分析项目入口点和路由配置，识别所有API端点和前端路由",
  "Additional_information": "项目路径：C:\\Users\\<USER>\\Desktop\\dify-main\n基于之前的架构分析，现在需要深入分析项目的入口点和路由配置。\n\n重点关注：\n1. 后端API入口点：\n   - 分析api/app.py和api/app_factory.py的主要路由配置\n   - 查看controllers目录下的所有控制器和路由定义\n   - 识别所有API端点及其功能\n   - 分析认证和授权机制\n\n2. 前端路由入口点：\n   - 分析web/app目录下的路由结构\n   - 查看Next.js的App Router配置\n   - 识别所有前端页面路由\n   - 分析前端路由与API的对应关系\n\n3. 入口点安全分析：\n   - 检查入口点的认证机制\n   - 分析中间件配置\n   - 识别潜在的未授权访问点\n   - 检查输入验证和过滤机制\n\n请详细列出所有发现的API端点、前端路由，并分析其安全配置和潜在的攻击面。"
}
2025-08-19 15:53:12,692 - INFO - TOKEN_USAGE - Model: glm-4.5, Prompt: 44522, Completion: 425, Total: 44947
2025-08-19 15:53:12,730 - INFO - NewTask called with: 分析项目入口点和路由配置，识别所有API端点和前端路由
2025-08-19 15:53:12,731 - INFO - 
📋 [TaskStack] 添加单个任务
2025-08-19 15:53:12,731 - INFO -    详情: 任务: 分析项目入口点和路由配置，识别所有API端点和前端路由
2025-08-19 15:53:12,731 - INFO -    📚 当前任务树 (共 2 个任务):
2025-08-19 15:53:12,731 - INFO - ├── ✅ 初始化项目并判断框架类型，分析项目整体架构 (ID: a50d77ba)
2025-08-19 15:53:12,731 - INFO - └── 🔄 分析项目入口点和路由配置，识别所有API端点和前端路由 (ID: 82d7703a)  <== [正在执行]
2025-08-19 15:53:12,731 - INFO - 
2025-08-19 15:53:12,732 - INFO - 🧠 正在为新任务 '分析项目入口点和路由配置，识别所有API端点和前端路由...' 检索历史经验...
2025-08-19 15:53:12,835 - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/embeddings "HTTP/1.1 200 OK"
2025-08-19 15:53:12,843 - INFO - 🔍 根据查询 '分析项目入口点和路由配置，识别所有API端点和前端路由...' 找到了 2 条相关历史记录。内容摘要如下:
  1. 1. **项目概述**：Dify是一个AI应用开发平台，版本1.7.2，采用前后端分离架构。  2. **整体架构**：前端(web目录)基于Next.js，后端(api目录)基于Flask，支持多种...
  2. 【工具调用】: 调用 `list_directory`，参数: {'directory_path': 'C:\\Users\\<USER>\\Desktop\\dify-main\\api\\mode...
2025-08-19 15:53:18,390 - INFO - HTTP Request: POST http://**************:8301/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-19 15:53:19,194 - INFO - 📞 工具调用: read_file 参数: {
  "file_path": "C:\\Users\\<USER>\\Desktop\\dify-main\\api\\app.py"
}
2025-08-19 15:53:19,194 - INFO - TOKEN_USAGE - Model: glm-4.5, Prompt: 15401, Completion: 52, Total: 15453
2025-08-19 15:53:24,294 - INFO - HTTP Request: POST http://**************:8301/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-19 15:53:25,024 - INFO - 📞 工具调用: read_file 参数: {
  "file_path": "C:\\Users\\<USER>\\Desktop\\dify-main\\api\\app_factory.py"
}
2025-08-19 15:53:25,024 - INFO - TOKEN_USAGE - Model: glm-4.5, Prompt: 15883, Completion: 49, Total: 15932
2025-08-19 15:53:28,001 - INFO - 
--- 流程执行完毕，正在清理所有异步资源 ---
2025-08-19 15:53:28,011 - INFO - 
--- Final Token Usage Summary ---
Model: glm-4.5
Total Prompt Tokens: 806836
Total Completion Tokens: 4699
Total Tokens Used: 811535
---------------------------------
2025-08-19 15:53:28,011 - INFO - 
--- 所有资源已成功清理 ---
